-- Create users table

CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    serial_no VARCHAR(10),
    username VARCHAR(50) NOT NULL UNIQUE,
    user_email VARCHAR(100) NOT NULL UNIQUE,
    employee_name VARCHAR(100) NOT NULL,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL,
    date DATE NOT NULL
);

-- Insert initial users
INSERT INTO users (serial_no, username, user_email, employee_name, password, role, date)
VALUES 
('001', 'admin', '<EMAIL>', 'Admin User', '1234', 'MANAGEMENT', CURRENT_DATE),
('002', 'admin1', '<EMAIL>', 'Engineer User', '1234', 'ENGINEER', CURRENT_DATE);

-- Create Material Requisitions table if not exists
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='material_requisitions' and xtype='U')
CREATE TABLE material_requisitions (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    requisition_id VARCHAR(50) NOT NULL,
    yard_number VARCHAR(50) NOT NULL,
    contractor_name VARCHAR(255) NOT NULL,
    project_name VARCHAR(255) NOT NULL,
    created_date DATETIME DEFAULT GETDATE(),
    status VARCHAR(20) DEFAULT 'PENDING'
);

-- Create Material Requisition Items table if not exists
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='material_requisition_items' and xtype='U')
CREATE TABLE material_requisition_items (
    id BIGINT IDENTITY(1,1) PRIMARY KEY,
    material_requisition_id BIGINT,
    unique_code VARCHAR(50) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    unit_of_measure VARCHAR(50) NOT NULL,
    quantity DECIMAL(10,2) NOT NULL,
    FOREIGN KEY (material_requisition_id) REFERENCES material_requisitions(id)
); 