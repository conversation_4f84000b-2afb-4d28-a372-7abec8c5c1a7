# Server Configuration
spring.mvc.pathmatch.matching-strategy=ant-path-matcher

#server.port=8080
server.port=8092
server.servlet.context-path=/synergy

# SQL Server Configuration
spring.datasource.url=*********************************************************************************************************************************************************************************;
spring.datasource.username=synergy@synergy-prd
spring.datasource.password=Gemba@2024GC
spring.datasource.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver

#prd
# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.SQLServerDialect

# SQL Initialization
spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath:data.sql
spring.sql.init.encoding=UTF-8
spring.sql.init.separator=;
spring.sql.init.continue-on-error=true

# Logging Configuration
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# Swagger Configuration
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.tryItOutEnabled=true
springdoc.swagger-ui.filter=true
springdoc.swagger-ui.show-extensions=true
springdoc.swagger-ui.show-common-extensions=true
# Fix for Swagger UI path
springdoc.swagger-ui.disable-swagger-default-url=true
springdoc.swagger-ui.configUrl=/synergy/api-docs/swagger-config
springdoc.swagger-ui.url=/synergy/api-docs

spring.mail.host=smtp-relay.brevo.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=mE4q3NdF6fOaAP7L

spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
spring.mail.properties.mail.debug=true
spring.mail.properties.mail.smtp.from=<EMAIL>

# Multipart File Upload Configuration
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
spring.servlet.multipart.file-size-threshold=2KB

# Application URLs
app.frontend.url=http://localhost:4200
app.bidding-form.path=/biddingform

# Application URLs - CHANGE THIS TO  PRODUCTION DOMAIN
#frontend domain
#app.frontend.url=http://gemba-dev.centralindia.cloudapp.azure.com
#app.bidding-form.path=/biddingform
