package com.synergy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;

public class VendorQuotationItemDTO {
    private Long id;
    private String uniqueCode;
    private String productName;
    private Integer availableQuantity;
    private Double unitPrice;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", shape = JsonFormat.Shape.STRING)
    private Date deliveryDate;
    
    private Double totalCost;
    private Boolean selected;
    private Integer originalQuantity; // Original quantity requested in PR
    private String materialFamily; // Material family from ShipbuildersItemEntity
    private Integer changingQuantity; // Quantity change made by engineering department

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUniqueCode() {
        return uniqueCode;
    }

    public void setUniqueCode(String uniqueCode) {
        this.uniqueCode = uniqueCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getAvailableQuantity() {
        return availableQuantity;
    }

    public void setAvailableQuantity(Integer availableQuantity) {
        this.availableQuantity = availableQuantity;
    }

    public Double getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(Double unitPrice) {
        this.unitPrice = unitPrice;
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public Double getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(Double totalCost) {
        this.totalCost = totalCost;
    }

    public Boolean getSelected() {
        return selected;
    }

    public void setSelected(Boolean selected) {
        this.selected = selected;
    }

    public Integer getOriginalQuantity() {
        return originalQuantity;
    }

    public void setOriginalQuantity(Integer originalQuantity) {
        this.originalQuantity = originalQuantity;
    }

    public String getMaterialFamily() {
        return materialFamily;
    }

    public void setMaterialFamily(String materialFamily) {
        this.materialFamily = materialFamily;
    }

    public Integer getChangingQuantity() {
        return changingQuantity;
    }

    public void setChangingQuantity(Integer changingQuantity) {
        this.changingQuantity = changingQuantity;
    }
}
