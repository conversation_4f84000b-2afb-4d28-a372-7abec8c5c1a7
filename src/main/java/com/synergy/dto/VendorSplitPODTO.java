package com.synergy.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Data transfer object for vendor-specific PO details in management tab")
public class VendorSplitPODTO {
    
    @Schema(description = "Original Purchase Request ID", example = "100")
    private Long originalPrId;
    
    @Schema(description = "Original Purchase Request ID string", example = "PR-20250530-0100")
    private String originalPrIdString;
    
    @Schema(description = "Vendor-specific PO ID. Format: 'prId' for single vendor, 'prId-sequence' for multiple vendors", example = "100")
    private String vendorPoId;
    
    @Schema(description = "Vendor ID", example = "15")
    private Long vendorId;
    
    @Schema(description = "Vendor company name", example = "ABC Steel Suppliers")
    private String vendorCompanyName;
    
    @Schema(description = "Vendor contact name", example = "John Smith")
    private String vendorName;
    
    @Schema(description = "Vendor email", example = "<EMAIL>")
    private String vendorEmail;
    
    @Schema(description = "Vendor contact number", example = "+91-9876543210")
    private String vendorContactNumber;

    @Schema(description = "Vendor address", example = "123 Industrial Area, Mumbai, Maharashtra, India")
    private String vendorAddress;

    @Schema(description = "Yard number", example = "YRD001")
    private String yardNumber;
    
    @Schema(description = "Project name", example = "Shipbuilding Project A")
    private String projectName;
    
    @Schema(description = "Contractor/Client name", example = "Oceanic Shipbuilders")
    private String contractorName;

    @Schema(description = "Date when the purchase request was created", example = "2024-05-30T10:30:00.000Z")
    private Date createdDate;

    @Schema(description = "List of line items for this vendor")
    private List<ApprovedPOLineItemDTO> lineItems;
    
    @Schema(description = "Grand total for this vendor's items", example = "250000.00")
    private BigDecimal vendorGrandTotal;
    
    @Schema(description = "Revised grand total for this vendor (if payment has been revised)", example = "240000.00")
    private BigDecimal vendorRevisedGrandTotal;
    
    @Schema(description = "Current status of the purchase request", example = "IN_PROGRESS_MANAGEMENT")
    private String status;
    
    @Schema(description = "Number of line items for this vendor", example = "2")
    private Integer lineItemCount;
}
