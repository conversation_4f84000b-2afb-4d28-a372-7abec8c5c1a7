package com.synergy.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.util.StdConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.List;
import java.util.Date;
import java.util.ArrayList;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Data transfer object for purchase request information")
public class PurchaseRequestDTO {
    @Schema(description = "Unique identifier of the purchase request", example = "1")
    private Long id;

    @Schema(description = "Purchase request ID (formatted identifier)", example = "PR-2023-001")
    private String prId;

    @Schema(description = "Yard number", example = "Y123")
    private String yardNumber;

    @Schema(description = "Client name (from Material Requisition)", example = "Client Company Name")
    private String contractorName;

    @JsonDeserialize(converter = ContractorNamesConverter.class)
    @Schema(description = "List of contractor names associated with this purchase request")
    private List<String> contractorNames;

    @Schema(description = "Name of the project", example = "Shipbuilding Project A")
    private String projectName;

    @Schema(description = "Date when the purchase request was created")
    private Date createdDate;

    @Schema(description = "Current status of the purchase request",
            example = "APPROVED",
            allowableValues = {"DRAFT", "PENDING_APPROVAL", "PENDING_QUANTITY_APPROVAL", "APPROVED", "REJECTED", "PENDING_VENDOR_SELECTION"})
    private String status;

    @Schema(description = "Type of purchase request", example = "REGULAR", allowableValues = {"REGULAR", "INSTANT"})
    private String prType;

    @Schema(description = "Reason for rejection (only present when status is REJECTED)",
            example = "Budget constraints")
    private String rejectionReason;

    @Schema(description = "Date when the purchase request was approved (only present when status is APPROVED)")
    private Date approvalDate;


    @JsonDeserialize(converter = VendorNamesConverter.class)
    @Schema(description = "List of vendor names associated with this purchase request")
    private List<String> vendorNames;

    @Schema(description = "ID of the material requisition (if PR was created from MR)")
    private Long materialRequisitionId;

    @Schema(description = "List of line items in the purchase request")
    private List<PurchaseRequestItemDTO> lineItems;

    @Schema(description = "Number of line items in the purchase request")
    private Integer lineItemCount;

    @Schema(description = "Flag indicating if quantities have been updated (only for REGULAR PRs)")
    private Boolean quantityUpdated;

    @Schema(description = "Flag indicating if RFQ emails have been reshared to vendors")
    private Boolean isReshared;

    @Schema(description = "Revised grand total amount (if payment has been revised in management)")
    private BigDecimal revisedGrandTotal;

    @Schema(description = "Grand total amount calculated from line items (for instant PRs)")
    private BigDecimal grandTotal;

    @Schema(description = "Optional remarks or notes for the purchase request", example = "Urgent requirement for project completion")
    private String remarks;

    // Custom getter for quantityUpdated
    public Boolean getQuantityUpdated() {
        return "REGULAR".equals(prType) ? quantityUpdated : null;
    }
}

class ContractorNamesConverter extends StdConverter<Object, List<String>> {
    @Override
    public List<String> convert(Object value) {
        List<String> result = new ArrayList<>();
        if (value instanceof String) {
            result.add((String) value);
        } else if (value instanceof List) {
            result.addAll((List<String>) value);
        }
        return result;
    }
}

class VendorNamesConverter extends StdConverter<Object, List<String>> {
    @Override
    public List<String> convert(Object value) {
        List<String> result = new ArrayList<>();
        if (value instanceof String) {
            result.add((String) value);
        } else if (value instanceof List) {
            result.addAll((List<String>) value);
        }
        return result;
    }
}
