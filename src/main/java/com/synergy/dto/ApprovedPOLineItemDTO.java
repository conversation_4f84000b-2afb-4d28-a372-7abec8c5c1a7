package com.synergy.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Data transfer object for approved PO line item details")
public class ApprovedPOLineItemDTO {

    @Schema(description = "Unique code for the item", example = "UC001")
    private String uniqueCode;

    @Schema(description = "Item code", example = "01")
    private String itemCode;

    @Schema(description = "Product/Item description", example = "High-strength marine steel")
    private String description;
    
    @Schema(description = "Material family", example = "Steel Plates")
    private String materialFamily;
    
    @Schema(description = "Quantity", example = "13")
    private Integer quantity;
    
    @Schema(description = "Unit of measure", example = "Tons")
    private String unitOfMeasure;
    
    @Schema(description = "Rate per unit", example = "50000.00")
    private BigDecimal rateApproved;
    
    @Schema(description = "Total amount for this line item", example = "650000.00")
    private BigDecimal total;
}
