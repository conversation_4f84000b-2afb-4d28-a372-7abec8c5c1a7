package com.synergy.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Lightweight DTO for purchase requests used in dashboard views
 * Contains only the essential fields needed for list displays
 */
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Lightweight data transfer object for purchase request dashboard views")
public class PurchaseRequestLightDTO {
    @Schema(description = "Unique identifier of the purchase request", example = "1")
    private Long id;

    @Schema(description = "Purchase request ID (formatted identifier)", example = "PR-2023-001")
    private String prId;

    @Schema(description = "Date when the purchase request was created")
    private Date createdDate;

    @Schema(description = "Client name (first contractor name)")
    private String clientName;

    @Schema(description = "Yard number", example = "Y123")
    private String yardNumber;

    @Schema(description = "Name of the project", example = "Shipbuilding Project A")
    private String projectName;

    @Schema(description = "Number of line items in the purchase request")
    private Integer lineItemCount;

    @Schema(description = "Current status of the purchase request",
            example = "APPROVED",
            allowableValues = {"DRAFT", "PENDING_APPROVAL", "PENDING_QUANTITY_APPROVAL", "APPROVED", "REJECTED", "PENDING_VENDOR_SELECTION", "IN_PROGRESS_MANAGEMENT"})
    private String status;

    @Schema(description = "Type of purchase request",
            example = "REGULAR",
            allowableValues = {"REGULAR", "INSTANT"})
    private String prType;

    @Schema(description = "RFQ status indicating vendor quotation responses",
            example = "Reply Pending",
            allowableValues = {"Reply Missing", "Reply Received", "Reply Pending"})
    private String rfqStatus;

    @Schema(description = "Grand total amount calculated from line items (for instant PRs)")
    private BigDecimal grandTotal;

    @Schema(description = "Optional remarks or notes for the purchase request", example = "Urgent requirement for project completion")
    private String remarks;

    @Schema(description = "Comma-separated list of unique material families from line items", example = "Bar, Steel Plates")
    private String materialFamilies;

    /**
     * Constructor for JPQL query projection
     */
    public PurchaseRequestLightDTO(Long id, String prId, Date createdDate, String clientName,
                                  String yardNumber, String projectName, Integer lineItemCount, String status, String prType) {
        this.id = id;
        this.prId = prId;
        this.createdDate = createdDate;
        this.clientName = clientName;
        this.yardNumber = yardNumber;
        this.projectName = projectName;
        this.lineItemCount = lineItemCount;
        this.status = status;
        this.prType = prType;
    }

    /**
     * Constructor for JPQL query projection with RFQ status
     */
    public PurchaseRequestLightDTO(Long id, String prId, Date createdDate, String clientName,
                                  String yardNumber, String projectName, Integer lineItemCount, String status, String prType, String rfqStatus) {
        this.id = id;
        this.prId = prId;
        this.createdDate = createdDate;
        this.clientName = clientName;
        this.yardNumber = yardNumber;
        this.projectName = projectName;
        this.lineItemCount = lineItemCount;
        this.status = status;
        this.prType = prType;
        this.rfqStatus = rfqStatus;
    }

    /**
     * Constructor for JPQL query projection with RFQ status and remarks
     */
    public PurchaseRequestLightDTO(Long id, String prId, Date createdDate, String clientName,
                                  String yardNumber, String projectName, Integer lineItemCount, String status, String prType, String rfqStatus, String remarks) {
        this.id = id;
        this.prId = prId;
        this.createdDate = createdDate;
        this.clientName = clientName;
        this.yardNumber = yardNumber;
        this.projectName = projectName;
        this.lineItemCount = lineItemCount;
        this.status = status;
        this.prType = prType;
        this.rfqStatus = rfqStatus;
        this.remarks = remarks;
    }
}
