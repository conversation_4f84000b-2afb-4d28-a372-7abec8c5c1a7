package com.synergy.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Data transfer object for payment revision request")
public class PaymentRevisionDTO {

    @Schema(description = "New revised grand total amount", example = "800000.00", required = true)
    private BigDecimal newGrandTotal;
}
