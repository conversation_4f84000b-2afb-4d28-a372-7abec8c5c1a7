package com.synergy.repository;

import com.synergy.entity.VendorQuotationEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface VendorQuotationRepository extends JpaRepository<VendorQuotationEntity, Long> {
    List<VendorQuotationEntity> findByPurchaseRequestId(Long purchaseRequestId);
    List<VendorQuotationEntity> findByVendorSrNo(Long srNo);
    List<VendorQuotationEntity> findByPurchaseRequestIdAndVendorSrNo(Long purchaseRequestId, Long vendorSrNo);

    // Batch method for performance optimization
    List<VendorQuotationEntity> findByPurchaseRequestIdIn(List<Long> purchaseRequestIds);
}
