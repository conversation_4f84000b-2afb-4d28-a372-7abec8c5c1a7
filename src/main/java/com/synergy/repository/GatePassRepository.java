package com.synergy.repository;

import com.synergy.entity.GatePassEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface GatePassRepository extends JpaRepository<GatePassEntity, Long> {
    
    /**
     * Find gate pass by gate pass ID
     */
    Optional<GatePassEntity> findByGatePassId(String gatePassId);
    
    /**
     * Find all gate passes for a specific vendor PO
     */
    List<GatePassEntity> findByVendorPoId(String vendorPoId);
    
    /**
     * Find all gate passes for a specific original PR
     */
    List<GatePassEntity> findByOriginalPrId(Long originalPrId);
    
    /**
     * Find all gate passes by status
     */
    List<GatePassEntity> findByStatusIgnoreCase(String status);
    
    /**
     * Find all gate passes for a specific vendor
     */
    List<GatePassEntity> findByVendorId(Long vendorId);
    
    /**
     * Check if gate pass exists for vendor PO
     */
    boolean existsByVendorPoId(String vendorPoId);
    
    /**
     * Get the next sequence number for a vendor PO
     */
    @Query("SELECT COUNT(g) + 1 FROM GatePassEntity g WHERE g.vendorPoId = :vendorPoId")
    Long getNextSequenceForVendorPo(@Param("vendorPoId") String vendorPoId);
    
    /**
     * Find all gate passes ordered by created date descending
     */
    List<GatePassEntity> findAllByOrderByCreatedDateDesc();
    
    /**
     * Find gate passes by status ordered by expected date
     */
    List<GatePassEntity> findByStatusIgnoreCaseOrderByExpectedDateAsc(String status);

    /**
     * Find gate passes by status ordered by issued date descending
     */
    List<GatePassEntity> findByStatusIgnoreCaseOrderByIssuedDateDesc(String status);
}
