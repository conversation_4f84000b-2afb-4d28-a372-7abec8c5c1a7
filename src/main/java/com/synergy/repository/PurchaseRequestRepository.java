package com.synergy.repository;

import com.synergy.entity.PurchaseRequestEntity;
import com.synergy.entity.VendorEntity;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface PurchaseRequestRepository extends JpaRepository<PurchaseRequestEntity, Long> {
	Optional<PurchaseRequestEntity> findByPrId(String prId);

	List<PurchaseRequestEntity> findAllByOrderByCreatedDateDesc();

	List<PurchaseRequestEntity> findByPrTypeIgnoreCase(String prType);

	List<PurchaseRequestEntity> findByVendorsContaining(VendorEntity vendor);

	Page<PurchaseRequestEntity> findAllByOrderByCreatedDateDesc(Pageable pageable);

	List<PurchaseRequestEntity> findByStatus(String status);

	Page<PurchaseRequestEntity> findByStatus(String status, Pageable pageable);
	List<PurchaseRequestEntity> findByStatusIgnoreCase(String status);


	// Optimized query to fetch PRs by multiple statuses
	@Query("SELECT pr FROM PurchaseRequestEntity pr WHERE pr.status IN :statuses ORDER BY pr.createdDate DESC")
	List<PurchaseRequestEntity> findByStatusIn(List<String> statuses);

	// Optimized query to fetch PRs by status
	@Query("SELECT pr FROM PurchaseRequestEntity pr WHERE pr.status = :status ORDER BY pr.createdDate DESC")
	List<PurchaseRequestEntity> findByStatusOptimized(String status);

	// Ultra-optimized native SQL query for dashboard views
	// This query directly fetches only the essential fields needed for the dashboard
	// and only includes PRs with vendors
	// Uses JOINs to avoid N+1 query problem
	// Groups by PR ID to avoid duplicates
	// Uses contractorName directly from the purchase_requests table
	// Includes RFQ status calculation based on vendor quotations
	@Query(value = "SELECT pr.id, pr.pr_id, pr.created_date, " +
			"pr.contractor_name as client_name, " +
			"pr.yard_number, pr.project_name, " +
			"COUNT(DISTINCT pri.id) as line_item_count, " +
			"pr.status, pr.pr_type, " +
			"CASE " +
			"  WHEN COUNT(DISTINCT vq.id) = 0 THEN 'Reply Missing' " +
			"  WHEN COUNT(DISTINCT vq.id) = COUNT(DISTINCT prv.vendor_id) THEN 'Reply Received' " +
			"  ELSE 'Reply Pending' " +
			"END as rfq_status, " +
			"(SELECT pr2.remarks FROM purchase_requests pr2 WHERE pr2.id = pr.id) as remarks " +
			"FROM purchase_requests pr " +
			"LEFT JOIN purchase_request_items pri ON pri.purchase_request_id = pr.id " +
			"LEFT JOIN purchase_request_vendors prv ON prv.purchase_request_id = pr.id " +
			"LEFT JOIN vendor_quotations vq ON vq.purchase_request_id = pr.id " +
			"WHERE pr.status = :status " +
			"AND EXISTS (SELECT 1 FROM purchase_request_vendors prv2 WHERE prv2.purchase_request_id = pr.id) " +
			"GROUP BY pr.id, pr.pr_id, pr.created_date, pr.contractor_name, pr.yard_number, pr.project_name, pr.status, pr.pr_type " +
			"ORDER BY pr.created_date DESC",
			nativeQuery = true)
	List<Object[]> findLightDTOsByStatusWithVendorsNative(String status);

	// Ultra-optimized native SQL query for In-Progress Management dashboard
	// Fetches PRs with status IN_PROGRESS_MANAGEMENT
	// Uses JOINs to avoid N+1 query problem
	// Groups by PR ID to avoid duplicates
	// Uses contractorName directly from the purchase_requests table
	@Query(value = "SELECT pr.id, pr.pr_id, pr.created_date, " +
			"pr.contractor_name as client_name, " +
			"pr.yard_number, pr.project_name, " +
			"COUNT(DISTINCT pri.id) as line_item_count, " +
			"pr.status, pr.pr_type, " +
			"(SELECT pr2.remarks FROM purchase_requests pr2 WHERE pr2.id = pr.id) as remarks, " +
			"STRING_AGG(pri.material_family, ', ') as material_families " +
			"FROM purchase_requests pr " +
			"LEFT JOIN purchase_request_items pri ON pri.purchase_request_id = pr.id " +
			"WHERE pr.status = 'IN_PROGRESS_MANAGEMENT' " +
			"GROUP BY pr.id, pr.pr_id, pr.created_date, pr.contractor_name, pr.yard_number, pr.project_name, pr.status, pr.pr_type " +
			"ORDER BY pr.created_date DESC",
			nativeQuery = true)
	List<Object[]> findLightDTOsForInProgressManagementNative();

	// Ultra-optimized native SQL query for PR Creation dashboard
	// Fetches PRs with status PENDING_VENDOR_SELECTION only
	// Uses JOINs to avoid N+1 query problem
	// Groups by PR ID to avoid duplicates
	// Uses contractorName directly from the purchase_requests table
	@Query(value = "SELECT pr.id, pr.pr_id, pr.created_date, " +
			"pr.contractor_name as client_name, " +
			"pr.yard_number, pr.project_name, " +
			"COUNT(DISTINCT pri.id) as line_item_count, " +
			"pr.status, pr.pr_type, " +
			"(SELECT pr2.remarks FROM purchase_requests pr2 WHERE pr2.id = pr.id) as remarks, " +
			"STRING_AGG(pri.material_family, ', ') as material_families " +
			"FROM purchase_requests pr " +
			"LEFT JOIN purchase_request_items pri ON pri.purchase_request_id = pr.id " +
			"WHERE pr.status = 'PENDING_VENDOR_SELECTION' " +
			"GROUP BY pr.id, pr.pr_id, pr.created_date, pr.contractor_name, pr.yard_number, pr.project_name, pr.status, pr.pr_type " +
			"ORDER BY pr.created_date DESC",
			nativeQuery = true)
	List<Object[]> findLightDTOsForCreationNative();

		// Date range filtering methods
		@Query("SELECT pr FROM PurchaseRequestEntity pr WHERE pr.createdDate BETWEEN :fromDate AND :toDate ORDER BY pr.createdDate DESC")
		List<PurchaseRequestEntity> findByCreatedDateBetween(@Param("fromDate") Date fromDate, @Param("toDate") Date toDate);

		@Query("SELECT pr FROM PurchaseRequestEntity pr WHERE pr.createdDate BETWEEN :fromDate AND :toDate AND pr.status = :status ORDER BY pr.createdDate DESC")
		List<PurchaseRequestEntity> findByCreatedDateBetweenAndStatus(@Param("fromDate") Date fromDate, @Param("toDate") Date toDate, @Param("status") String status);

		@Query("SELECT pr FROM PurchaseRequestEntity pr WHERE pr.createdDate BETWEEN :fromDate AND :toDate AND pr.status IN :statuses ORDER BY pr.createdDate DESC")
		List<PurchaseRequestEntity> findByCreatedDateBetweenAndStatusIn(@Param("fromDate") Date fromDate, @Param("toDate") Date toDate, @Param("statuses") List<String> statuses);

		// Date range filtering with prType support
		@Query("SELECT pr FROM PurchaseRequestEntity pr WHERE pr.createdDate BETWEEN :fromDate AND :toDate AND (:status IS NULL OR pr.status = :status) AND (:prType IS NULL OR pr.prType = :prType) ORDER BY pr.createdDate DESC")
		List<PurchaseRequestEntity> findByCreatedDateBetweenAndStatusAndPrType(@Param("fromDate") Date fromDate, @Param("toDate") Date toDate, @Param("status") String status, @Param("prType") String prType);

		// Light DTO queries with date range filtering
		@Query(value = "SELECT pr.id, pr.pr_id, pr.created_date, " +
				"pr.contractor_name as client_name, " +
				"pr.yard_number, pr.project_name, " +
				"COUNT(DISTINCT pri.id) as line_item_count, " +
				"pr.status, pr.pr_type, " +
				"CASE " +
				"  WHEN COUNT(DISTINCT vq.id) = 0 THEN 'Reply Missing' " +
				"  WHEN COUNT(DISTINCT vq.id) = COUNT(DISTINCT prv.vendor_id) THEN 'Reply Received' " +
				"  ELSE 'Reply Pending' " +
				"END as rfq_status, " +
				"(SELECT pr2.remarks FROM purchase_requests pr2 WHERE pr2.id = pr.id) as remarks " +
				"FROM purchase_requests pr " +
				"LEFT JOIN purchase_request_items pri ON pri.purchase_request_id = pr.id " +
				"LEFT JOIN purchase_request_vendors prv ON prv.purchase_request_id = pr.id " +
				"LEFT JOIN vendor_quotations vq ON vq.purchase_request_id = pr.id " +
				"WHERE pr.created_date BETWEEN :fromDate AND :toDate " +
				"AND (:status IS NULL OR pr.status = :status) " +
				"AND EXISTS (SELECT 1 FROM purchase_request_vendors prv2 WHERE prv2.purchase_request_id = pr.id) " +
				"GROUP BY pr.id, pr.pr_id, pr.created_date, pr.contractor_name, pr.yard_number, pr.project_name, pr.status, pr.pr_type " +
				"ORDER BY pr.created_date DESC",
				nativeQuery = true)
		List<Object[]> findLightDTOsByDateRangeAndStatusWithVendorsNative(@Param("fromDate") Date fromDate, @Param("toDate") Date toDate, @Param("status") String status);

		@Query(value = "SELECT pr.id, pr.pr_id, pr.created_date, " +
				"pr.contractor_name as client_name, " +
				"pr.yard_number, pr.project_name, " +
				"COUNT(DISTINCT pri.id) as line_item_count, " +
				"pr.status, pr.pr_type " +
				"FROM purchase_requests pr " +
				"LEFT JOIN purchase_request_items pri ON pri.purchase_request_id = pr.id " +
				"WHERE pr.created_date BETWEEN :fromDate AND :toDate " +
				"AND (:status IS NULL OR pr.status = :status) " +
				"GROUP BY pr.id, pr.pr_id, pr.created_date, pr.contractor_name, pr.yard_number, pr.project_name, pr.status, pr.pr_type " +
				"ORDER BY pr.created_date DESC",
				nativeQuery = true)
		List<Object[]> findLightDTOsByDateRangeAndStatusNative(@Param("fromDate") Date fromDate, @Param("toDate") Date toDate, @Param("status") String status);

		// Light DTO queries with date range, status and prType filtering
		@Query(value = "SELECT pr.id, pr.pr_id, pr.created_date, " +
				"pr.contractor_name as client_name, " +
				"pr.yard_number, pr.project_name, " +
				"COUNT(DISTINCT pri.id) as line_item_count, " +
				"pr.status, pr.pr_type, " +
				"CASE " +
				"  WHEN COUNT(DISTINCT vq.id) = 0 THEN 'Reply Missing' " +
				"  WHEN COUNT(DISTINCT vq.id) = COUNT(DISTINCT prv.vendor_id) THEN 'Reply Received' " +
				"  ELSE 'Reply Pending' " +
				"END as rfq_status, " +
				"(SELECT pr2.remarks FROM purchase_requests pr2 WHERE pr2.id = pr.id) as remarks " +
				"FROM purchase_requests pr " +
				"LEFT JOIN purchase_request_items pri ON pri.purchase_request_id = pr.id " +
				"LEFT JOIN purchase_request_vendors prv ON prv.purchase_request_id = pr.id " +
				"LEFT JOIN vendor_quotations vq ON vq.purchase_request_id = pr.id " +
				"WHERE pr.created_date BETWEEN :fromDate AND :toDate " +
				"AND (:status IS NULL OR pr.status = :status) " +
				"AND (:prType IS NULL OR pr.pr_type = :prType) " +
				"AND EXISTS (SELECT 1 FROM purchase_request_vendors prv2 WHERE prv2.purchase_request_id = pr.id) " +
				"GROUP BY pr.id, pr.pr_id, pr.created_date, pr.contractor_name, pr.yard_number, pr.project_name, pr.status, pr.pr_type " +
				"ORDER BY pr.created_date DESC",
				nativeQuery = true)
		List<Object[]> findLightDTOsByDateRangeAndStatusAndPrTypeWithVendorsNative(@Param("fromDate") Date fromDate, @Param("toDate") Date toDate, @Param("status") String status, @Param("prType") String prType);

		@Query(value = "SELECT pr.id, pr.pr_id, pr.created_date, " +
				"pr.contractor_name as client_name, " +
				"pr.yard_number, pr.project_name, " +
				"COUNT(DISTINCT pri.id) as line_item_count, " +
				"pr.status, pr.pr_type " +
				"FROM purchase_requests pr " +
				"LEFT JOIN purchase_request_items pri ON pri.purchase_request_id = pr.id " +
				"WHERE pr.created_date BETWEEN :fromDate AND :toDate " +
				"AND (:status IS NULL OR pr.status = :status) " +
				"AND (:prType IS NULL OR pr.pr_type = :prType) " +
				"GROUP BY pr.id, pr.pr_id, pr.created_date, pr.contractor_name, pr.yard_number, pr.project_name, pr.status, pr.pr_type " +
				"ORDER BY pr.created_date DESC",
				nativeQuery = true)
		List<Object[]> findLightDTOsByDateRangeAndStatusAndPrTypeNative(@Param("fromDate") Date fromDate, @Param("toDate") Date toDate, @Param("status") String status, @Param("prType") String prType);
}
