package com.synergy.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.Date;

@Entity
@Table(name = "gate_passes")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GatePassEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "gate_pass_id", unique = true, nullable = false)
    private String gatePassId; // Format: PO{vendorPoId}-GP{sequence}
    
    @Column(name = "vendor_po_id", nullable = false)
    private String vendorPoId; // e.g., "647" or "647-1"
    
    @Column(name = "original_pr_id", nullable = false)
    private Long originalPrId;
    
    @Column(name = "original_pr_id_string")
    private String originalPrIdString;
    
    @Column(name = "vendor_id", nullable = false)
    private Long vendorId;
    
    @Column(name = "vendor_company_name")
    private String vendorCompanyName;
    
    @Column(name = "vendor_name")
    private String vendorName;
    
    @Column(name = "vendor_email")
    private String vendorEmail;
    
    @Column(name = "vendor_contact_number")
    private String vendorContactNumber;
    
    @Column(name = "yard_number")
    private String yardNumber;
    
    @Column(name = "project_name")
    private String projectName;
    
    @Column(name = "contractor_name")
    private String contractorName;
    
    @Column(name = "expected_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date expectedDate; // Delivery date from PDF generation
    
    @Column(name = "line_item_count")
    private Integer lineItemCount;
    
    @Column(name = "status")
    private String status; // PENDING, ISSUED, COMPLETED
    
    @Column(name = "created_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdDate;
    
    @Column(name = "issued_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date issuedDate;
    
    @Column(name = "completed_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date completedDate;
    
    @Column(name = "remarks")
    private String remarks;

    // Form fields
    @Column(name = "invoice_number")
    private String invoiceNumber;

    @Column(name = "vehicle_number")
    private String vehicleNumber;

    @Column(name = "driver_name")
    private String driverName;

    @Column(name = "vehicle_front_photo_path")
    private String vehicleFrontPhotoPath;

    @Column(name = "vehicle_back_photo_path")
    private String vehicleBackPhotoPath;

    @PrePersist
    protected void onCreate() {
        createdDate = new Date();
        if (status == null) {
            status = "PENDING";
        }
    }
}
