package com.synergy.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonManagedReference;

import java.util.Date;
import java.util.List;
import java.util.ArrayList;

@Entity
@Table(name = "grn")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GRNEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "grn_number", unique = true, nullable = false)
    private String grnNumber; // Auto-generated GRN number
    
    @Column(name = "gate_pass_id", nullable = false)
    private String gatePassId;
    
    @Column(name = "vendor_po_id", nullable = false)
    private String vendorPoId;
    
    @Column(name = "yard_number")
    private String yardNumber;
    
    @Column(name = "project_name")
    private String projectName;
    
    @Column(name = "vendor_company_name")
    private String vendorCompanyName;
    
    @Column(name = "quality_check")
    private Boolean qualityCheck;
    
    @Column(name = "status")
    private String status; // DRAFT, SUBMITTED, APPROVED, REJECTED
    
    @Column(name = "created_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdDate;
    
    @Column(name = "updated_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedDate;
    
    @Column(name = "submitted_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date submittedDate;
    
    @Column(name = "approved_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date approvedDate;
    
    @Column(name = "remarks")
    private String remarks;
    
    @OneToMany(mappedBy = "grn", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonManagedReference
    private List<GRNLineItemEntity> lineItems = new ArrayList<>();
    
    @PrePersist
    protected void onCreate() {
        createdDate = new Date();
        updatedDate = new Date();
        if (status == null) {
            status = "DRAFT";
        }
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedDate = new Date();
    }
}
