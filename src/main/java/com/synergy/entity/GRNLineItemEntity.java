package com.synergy.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonBackReference;

@Entity
@Table(name = "grn_line_items")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GRNLineItemEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne
    @JoinColumn(name = "grn_id", nullable = false)
    @JsonBackReference
    private GRNEntity grn;
    
    @Column(name = "item_code")
    private String itemCode;
    
    @Column(name = "description")
    private String description;
    
    @Column(name = "material_family")
    private String materialFamily;
    
    @Column(name = "qty_ordered")
    private Integer qtyOrdered;
    
    @Column(name = "qty_received")
    private Integer qtyReceived;
    
    @Column(name = "status")
    private String status;
    
    @Column(name = "location")
    private String location;
    
    @Column(name = "remarks")
    private String remarks;
    
    @Column(name = "quality_check_requirement")
    private String qualityCheckRequirement;
    
    @Column(name = "unique_code")
    private String uniqueCode;
}
