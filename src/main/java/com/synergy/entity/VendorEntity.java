package com.synergy.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;
import com.fasterxml.jackson.databind.ObjectMapper;

@Entity
@Table(name = "vendor_master")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VendorEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "sr_no", unique = true, nullable = false)
    private Long srNo;

    @Column(name = "company_name", length = 255)
    private String companyName;

    @Column(name = "gst_number", length = 50)
    private String gstNumber;

    @Column(name = "pan_no", length = 50)
    private String panNo;

    @Column(name = "vendor_name", length = 100)
    private String vendorName;

    @Column(name = "whatsapp_number", length = 50)
    private String whatsappNumber;

    @Column(name = "contact_number", length = 50)
    private String contactNumber;

    @Column(name = "email_id", length = 255)
    private String emailId;

    @Column(name = "address", columnDefinition = "text")
    private String address; // Stores a JSON array of address objects with type

    @Transient
    private transient ObjectMapper objectMapper = new ObjectMapper();

    @Column(name = "country", length = 100)
    private String country;

    @Column(name = "city", length = 100)
    private String city;

    @Column(name = "state", length = 100)
    private String state;


    @Column(name = "vendor_code", length = 50)
    private String vendorCode;

    @Column(name = "credit_days")
    private String creditDays;

    @Column(name = "credit_limit", precision = 18, scale = 2)
    private BigDecimal creditLimit;

    @Column(name = "remark", columnDefinition = "text")
    private String remark;

    @Column(name = "vendor_type", length = 100)
    private String vendorType;
}