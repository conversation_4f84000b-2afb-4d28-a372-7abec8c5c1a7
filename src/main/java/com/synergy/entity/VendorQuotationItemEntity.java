package com.synergy.entity;

import jakarta.persistence.*;
import com.fasterxml.jackson.annotation.JsonBackReference;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.Date;

@Entity
@Table(name = "vendor_quotation_items")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VendorQuotationItemEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "unique_code")
    private String uniqueCode;

    @Column(name = "product_name")
    private String productName;

    @Column(name = "available_quantity")
    private Integer availableQuantity;

    @Column(name = "unit_price")
    private Double unitPrice;

    @Column(name = "delivery_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date deliveryDate;

    @Column(name = "total_cost")
    private Double totalCost;

    @Column(name = "selected")
    private Boolean selected;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "vendor_quotation_id")
    @JsonBackReference
    private VendorQuotationEntity vendorQuotation;
}
