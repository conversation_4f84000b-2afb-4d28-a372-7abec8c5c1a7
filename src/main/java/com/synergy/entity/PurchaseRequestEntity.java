package com.synergy.entity;

import jakarta.persistence.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.fasterxml.jackson.annotation.JsonBackReference;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "purchase_requests")
@NoArgsConstructor
public class PurchaseRequestEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "pr_id", unique = true)
    private String prId;

    @Column(name = "yard_number")
    private String yardNumber;

    @Column(name = "contractor_name")
    private String contractorName;

    @ManyToMany
    @JoinTable(
        name = "purchase_request_contractors",
        joinColumns = @JoinColumn(name = "purchase_request_id"),
        inverseJoinColumns = @JoinColumn(name = "contractor_id")
    )
    private List<ContractorEntity> contractors = new ArrayList<>();

    @Column(name = "project_name")
    private String projectName;

    @Column(name = "created_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdDate;

    @Column(name = "status")
    private String status;

    @Column(name = "pr_type")
    private String prType; // REGULAR or INSTANT

    @Column(name = "quantity_updated")
    private Boolean quantityUpdated;

    @Column(name = "is_reshared")
    private Boolean isReshared = false;

    @Column(name = "rejection_reason")
    private String rejectionReason;

    @Column(name = "approval_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date approvalDate;

    @Column(name = "revised_grand_total", precision = 15, scale = 2)
    private BigDecimal revisedGrandTotal;

    @Column(name = "grand_total", precision = 15, scale = 2)
    private BigDecimal grandTotal;

    @Column(name = "remarks", columnDefinition = "TEXT")
    private String remarks;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "material_requisition_id")
    @JsonBackReference
    private MaterialRequisitionEntity materialRequisition;

    @ManyToMany
    @JoinTable(
        name = "purchase_request_vendors",
        joinColumns = @JoinColumn(name = "purchase_request_id"),
        inverseJoinColumns = @JoinColumn(name = "vendor_id")
    )
    private List<VendorEntity> vendors = new ArrayList<>();

    @OneToMany(mappedBy = "purchaseRequest", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonManagedReference
    private List<PurchaseRequestItemEntity> lineItems = new ArrayList<>();

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPrId() {
        return prId;
    }

    public void setPrId(String prId) {
        this.prId = prId;
    }

    public String getYardNumber() {
        return yardNumber;
    }

    public void setYardNumber(String yardNumber) {
        this.yardNumber = yardNumber;
    }

    public String getContractorName() {
        return contractorName;
    }

    public void setContractorName(String contractorName) {
        this.contractorName = contractorName;
    }

    public List<ContractorEntity> getContractors() {
        return contractors;
    }

    public void setContractors(List<ContractorEntity> contractors) {
        this.contractors = contractors;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPrType() {
        return prType;
    }

    public void setPrType(String prType) {
        this.prType = prType;
    }

    public Boolean getQuantityUpdated() {
        return quantityUpdated;
    }

    public void setQuantityUpdated(Boolean quantityUpdated) {
        this.quantityUpdated = quantityUpdated;
    }

    public Boolean getIsReshared() {
        return isReshared;
    }

    public void setIsReshared(Boolean isReshared) {
        this.isReshared = isReshared;
    }

    public String getRejectionReason() {
        return rejectionReason;
    }

    public void setRejectionReason(String rejectionReason) {
        this.rejectionReason = rejectionReason;
    }

    public Date getApprovalDate() {
        return approvalDate;
    }

    public void setApprovalDate(Date approvalDate) {
        this.approvalDate = approvalDate;
    }

    public BigDecimal getRevisedGrandTotal() {
        return revisedGrandTotal;
    }

    public void setRevisedGrandTotal(BigDecimal revisedGrandTotal) {
        this.revisedGrandTotal = revisedGrandTotal;
    }

    public BigDecimal getGrandTotal() {
        return grandTotal;
    }

    public void setGrandTotal(BigDecimal grandTotal) {
        this.grandTotal = grandTotal;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public MaterialRequisitionEntity getMaterialRequisition() {
        return materialRequisition;
    }

    public void setMaterialRequisition(MaterialRequisitionEntity materialRequisition) {
        this.materialRequisition = materialRequisition;
    }

    public List<VendorEntity> getVendors() {
        return vendors;
    }

    public void setVendors(List<VendorEntity> vendors) {
        this.vendors = vendors;
    }

    public List<PurchaseRequestItemEntity> getLineItems() {
        return lineItems;
    }

    public void setLineItems(List<PurchaseRequestItemEntity> lineItems) {
        this.lineItems = lineItems;
    }

    // Constructor for optimized queries
    public PurchaseRequestEntity(Long id, String prId, String yardNumber, String contractorName, String projectName,
                                Date createdDate, String status, String prType,
                                Boolean quantityUpdated, String rejectionReason, Date approvalDate) {
        this.id = id;
        this.prId = prId;
        this.yardNumber = yardNumber;
        this.contractorName = contractorName;
        this.projectName = projectName;
        this.createdDate = createdDate;
        this.status = status;
        this.prType = prType;
        this.quantityUpdated = quantityUpdated;
        this.rejectionReason = rejectionReason;
        this.approvalDate = approvalDate;

        // Initialize empty collections to avoid NPEs
        this.contractors = new ArrayList<>();
        this.vendors = new ArrayList<>();
        this.lineItems = new ArrayList<>();
    }
}
