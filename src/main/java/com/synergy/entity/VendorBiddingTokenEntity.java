package com.synergy.entity;

import jakarta.persistence.*;
import java.util.Date;

@Entity
@Table(name = "vendor_bidding_tokens")
public class VendorBiddingTokenEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "token", unique = true, nullable = false, length = 255)
    private String token;

    @ManyToOne
    @JoinColumn(name = "purchase_request_id", nullable = false)
    private PurchaseRequestEntity purchaseRequest;

    @ManyToOne
    @JoinColumn(name = "vendor_id", nullable = false)
    private VendorEntity vendor;

    @Column(name = "is_used", nullable = false)
    private Boolean isUsed = false;

    @Column(name = "created_date", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdDate;

    @Column(name = "used_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date usedDate;

    @Column(name = "quotation_id")
    private Long quotationId;

    @Column(name = "is_reshared", nullable = true)
    private Boolean isReshared = false;

    // Constructors
    public VendorBiddingTokenEntity() {
        this.createdDate = new Date();
        this.isUsed = false;
        this.isReshared = false;
    }

    public VendorBiddingTokenEntity(String token, PurchaseRequestEntity purchaseRequest, VendorEntity vendor) {
        this();
        this.token = token;
        this.purchaseRequest = purchaseRequest;
        this.vendor = vendor;
    }

    public VendorBiddingTokenEntity(String token, PurchaseRequestEntity purchaseRequest, VendorEntity vendor, Boolean isReshared) {
        this();
        this.token = token;
        this.purchaseRequest = purchaseRequest;
        this.vendor = vendor;
        this.isReshared = isReshared != null ? isReshared : false;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public PurchaseRequestEntity getPurchaseRequest() {
        return purchaseRequest;
    }

    public void setPurchaseRequest(PurchaseRequestEntity purchaseRequest) {
        this.purchaseRequest = purchaseRequest;
    }

    public VendorEntity getVendor() {
        return vendor;
    }

    public void setVendor(VendorEntity vendor) {
        this.vendor = vendor;
    }

    public Boolean getIsUsed() {
        return isUsed;
    }

    public void setIsUsed(Boolean isUsed) {
        this.isUsed = isUsed;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getUsedDate() {
        return usedDate;
    }

    public void setUsedDate(Date usedDate) {
        this.usedDate = usedDate;
    }

    public Long getQuotationId() {
        return quotationId;
    }

    public void setQuotationId(Long quotationId) {
        this.quotationId = quotationId;
    }

    public Boolean getIsReshared() {
        return isReshared;
    }

    public void setIsReshared(Boolean isReshared) {
        this.isReshared = isReshared;
    }

    // Helper methods
    public void markAsUsed() {
        this.isUsed = true;
        this.usedDate = new Date();
    }

    public boolean isValid() {
        return !this.isUsed;
    }
}
