package com.synergy.entity;

import jakarta.persistence.*;
import com.fasterxml.jackson.annotation.JsonBackReference;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;

@Entity
@Table(name = "purchase_request_items")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseRequestItemEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "unique_code")
    private String uniqueCode;

    @Column(name = "product_name")
    private String productName;

    @Column(name = "unit_of_measure")
    private String unitOfMeasure;

    @Column(name = "quantity")
    private Integer quantity;

    @Column(name = "rate", precision = 10, scale = 2)
    private BigDecimal rate;

    @Column(name = "total", precision = 10, scale = 2)
    private BigDecimal total;
    
    @Column(name = "item_name")
    private String itemName;

    @Column(name = "change_in_quantity")
    private Integer changeInQuantity;

    @Column(name = "quantity_updated")
    private Boolean quantityUpdated;

    @Column(name = "material_family")
    private String materialFamily;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "purchase_request_id")
    @JsonBackReference
    private PurchaseRequestEntity purchaseRequest;
}
