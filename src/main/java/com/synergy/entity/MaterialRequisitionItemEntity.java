package com.synergy.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import jakarta.persistence.*;

@Entity
@Table(name = "material_requisition_items")
public class MaterialRequisitionItemEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "unique_code")
    private String uniqueCode;

    @Column(name = "product_name")
    private String productName;

    @Column(name = "unit_of_measure")
    private String unitOfMeasure;

    @Column(name = "quantity")
    private Double quantity;

    @ManyToOne
    @JoinColumn(name = "material_requisition_id")
    @JsonBackReference
    private MaterialRequisitionEntity materialRequisition;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUniqueCode() {
        return uniqueCode;
    }

    public void setUniqueCode(String uniqueCode) {
        this.uniqueCode = uniqueCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    public MaterialRequisitionEntity getMaterialRequisition() {
        return materialRequisition;
    }

    public void setMaterialRequisition(MaterialRequisitionEntity materialRequisition) {
        this.materialRequisition = materialRequisition;
    }

    @Override
    public String toString() {
        return "MaterialRequisitionItemEntity{" +
                "id=" + id +
                ", uniqueCode='" + uniqueCode + '\'' +
                ", productName='" + productName + '\'' +
                ", unitOfMeasure='" + unitOfMeasure + '\'' +
                ", quantity=" + quantity +
                '}';
    }
}