package com.synergy.service;

import com.synergy.entity.PurchaseRequestEntity;
import com.synergy.entity.VendorBiddingTokenEntity;
import com.synergy.entity.VendorEntity;
import com.synergy.repository.VendorBiddingTokenRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
public class VendorBiddingTokenService {
    
    private static final Logger logger = LoggerFactory.getLogger(VendorBiddingTokenService.class);
    
    @Autowired
    private VendorBiddingTokenRepository tokenRepository;
    
    /**
     * Generate a new unique bidding token for a vendor and purchase request
     */
    @Transactional
    public String generateBiddingToken(PurchaseRequestEntity purchaseRequest, VendorEntity vendor) {
        return generateBiddingToken(purchaseRequest, vendor, false);
    }

    /**
     * Generate a new unique bidding token for a vendor and purchase request with reshared flag
     */
    @Transactional
    public String generateBiddingToken(PurchaseRequestEntity purchaseRequest, VendorEntity vendor, Boolean isReshared) {
        try {
            // Generate a unique token
            String token = UUID.randomUUID().toString();

            // Ensure token is unique (very unlikely to collide, but just in case)
            while (tokenRepository.findByToken(token).isPresent()) {
                token = UUID.randomUUID().toString();
            }

            // Create and save the token entity
            VendorBiddingTokenEntity tokenEntity = new VendorBiddingTokenEntity(token, purchaseRequest, vendor, isReshared);
            tokenRepository.save(tokenEntity);

            logger.info("Generated new bidding token for PR {} and vendor {} (reshared: {}): {}",
                       purchaseRequest.getId(), vendor.getSrNo(), isReshared, token);

            return token;
        } catch (Exception e) {
            logger.error("Error generating bidding token for PR {} and vendor {}: {}",
                        purchaseRequest.getId(), vendor.getSrNo(), e.getMessage(), e);
            throw new RuntimeException("Failed to generate bidding token", e);
        }
    }
    
    /**
     * Validate a bidding token and return the associated token entity if valid
     */
    public Optional<VendorBiddingTokenEntity> validateToken(String token) {
        try {
            Optional<VendorBiddingTokenEntity> tokenEntity = tokenRepository.findByToken(token);
            
            if (tokenEntity.isPresent()) {
                VendorBiddingTokenEntity entity = tokenEntity.get();
                if (entity.isValid()) {
                    logger.info("Token {} is valid for PR {} and vendor {}", 
                               token, entity.getPurchaseRequest().getId(), entity.getVendor().getSrNo());
                    return tokenEntity;
                } else {
                    logger.warn("Token {} has already been used", token);
                    return Optional.empty();
                }
            } else {
                logger.warn("Token {} not found", token);
                return Optional.empty();
            }
        } catch (Exception e) {
            logger.error("Error validating token {}: {}", token, e.getMessage(), e);
            return Optional.empty();
        }
    }
    
    /**
     * Mark a token as used when a quotation is submitted
     */
    @Transactional
    public void markTokenAsUsed(String token, Long quotationId) {
        try {
            Optional<VendorBiddingTokenEntity> tokenEntity = tokenRepository.findByToken(token);
            
            if (tokenEntity.isPresent()) {
                VendorBiddingTokenEntity entity = tokenEntity.get();
                entity.markAsUsed();
                entity.setQuotationId(quotationId);
                tokenRepository.save(entity);
                
                logger.info("Marked token {} as used for quotation {}", token, quotationId);
            } else {
                logger.warn("Attempted to mark non-existent token as used: {}", token);
            }
        } catch (Exception e) {
            logger.error("Error marking token {} as used: {}", token, e.getMessage(), e);
            throw new RuntimeException("Failed to mark token as used", e);
        }
    }
    
    /**
     * Invalidate all unused tokens for a specific PR and vendor
     * This is useful when resharing links - old tokens become invalid
     */
    @Transactional
    public void invalidateUnusedTokensForPRAndVendor(Long purchaseRequestId, Long vendorId) {
        try {
            List<VendorBiddingTokenEntity> unusedTokens = 
                tokenRepository.findUnusedTokensByPurchaseRequestAndVendor(purchaseRequestId, vendorId);
            
            for (VendorBiddingTokenEntity token : unusedTokens) {
                token.setIsUsed(true);
                token.setUsedDate(new Date());
                tokenRepository.save(token);
            }
            
            logger.info("Invalidated {} unused tokens for PR {} and vendor {}", 
                       unusedTokens.size(), purchaseRequestId, vendorId);
        } catch (Exception e) {
            logger.error("Error invalidating unused tokens for PR {} and vendor {}: {}", 
                        purchaseRequestId, vendorId, e.getMessage(), e);
            throw new RuntimeException("Failed to invalidate unused tokens", e);
        }
    }
    
    /**
     * Check if a vendor has any valid (unused) tokens for a purchase request
     */
    public boolean hasValidTokenForPR(Long purchaseRequestId, Long vendorId) {
        try {
            List<VendorBiddingTokenEntity> unusedTokens = 
                tokenRepository.findUnusedTokensByPurchaseRequestAndVendor(purchaseRequestId, vendorId);
            return !unusedTokens.isEmpty();
        } catch (Exception e) {
            logger.error("Error checking for valid tokens for PR {} and vendor {}: {}", 
                        purchaseRequestId, vendorId, e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * Get all tokens for a purchase request (for debugging/admin purposes)
     */
    public List<VendorBiddingTokenEntity> getTokensForPR(Long purchaseRequestId) {
        return tokenRepository.findByPurchaseRequestId(purchaseRequestId);
    }
}
