package com.synergy.service;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

@Service
public class EmailService {

    @Autowired
    private JavaMailSender mailSender;

    @Value("${spring.mail.properties.mail.smtp.from}")
    private String fromEmail;

    /**
     * Creates a simplified responsive HTML email template with the provided content
     * @param content The main content of the email
     * @return A complete HTML email with basic responsive design
     */
    private String createResponsiveEmailTemplate(String content) {
        return """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Synergy Shipbuilders</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333333; margin: 0; padding: 0;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #ffffff;">
                    <div style="padding: 20px 0; text-align: center; border-bottom: 1px solid #dddddd;">
                        <h2>Synergy Shipbuilders</h2>
                    </div>
                    <div style="padding: 20px 0;">
                        %s
                    </div>
                    <div style="padding: 20px 0; text-align: center; font-size: 12px; color: #666666; border-top: 1px solid #dddddd;">
                        &copy; 2024 Synergy Shipbuilders. All rights reserved.
                    </div>
                </div>
            </body>
            </html>
            """.formatted(content);
    }

    public void sendQuotationRequestEmail(String toEmail, String vendorName, Long prId, String formUrl) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);

            helper.setTo(toEmail);
            helper.setFrom(fromEmail);
            helper.setSubject("Official Invitation to Submit Quotation – Purchase Request " + prId);

            String htmlContent = """
                <p>Dear <strong>%s</strong>,</p>

                <p>We are pleased to extend an invitation to your esteemed organization to participate in the bidding process for Purchase Request <strong>#%d</strong> raised by Synergy Shipbuilders.</p>

                <p>Kindly submit your quotation by accessing the secure bidding form through the link below:</p>

                <p style="text-align: center;">
                    <a href="%s" style="display: inline-block; padding: 10px 20px; margin: 15px 0; background-color: #0066cc; color: #ffffff; text-decoration: none; border-radius: 4px; font-weight: bold;">Submit Quotation</a>
                </p>

                <p>If the button above doesn't work, you can copy and paste this link into your browser:</p>
                <p style="word-break: break-all; font-size: 12px;">%s</p>

                <p><strong>Important:</strong> This link will remain active only until the submission deadline. We recommend completing your response at the earliest convenience to ensure timely consideration.</p>

                <p>If you have any questions or require further assistance, please feel free to reach out to us.</p>

                <p>We look forward to receiving your valued proposal.</p>

                <p>
                Warm regards,<br>
                Procurement Team<br>
                Synergy Shipbuilders
                </p>
                """.formatted(vendorName, prId, formUrl, formUrl);

            // Apply the responsive template
            String emailContent = createResponsiveEmailTemplate(htmlContent);

            helper.setText(emailContent, true);
            System.out.println("Sending quantity changed email to: " + toEmail);
            try {
                mailSender.send(message);
                System.out.println("Quantity changed email sent successfully to: " + toEmail);
            } catch (Exception e) {
                System.err.println("Error sending quantity changed email to " + toEmail + ": " + e.getMessage());
                e.printStackTrace();
                throw e;
            }
        } catch (MessagingException e) {
            System.err.println("MessagingException in sendQuantityChangedEmail: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to send quantity changed email", e);
        }
    }

    public void sendQuotationStatusEmail(String toEmail, String vendorName, Long prId, String status) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);

            helper.setTo(toEmail);
            helper.setFrom(fromEmail);
            helper.setSubject("Quotation Status Update - PR#" + prId);

            String additionalInfo = status.equals("SELECTED") ?
                "We will contact you shortly with further details." :
                "Thank you for your participation.";

            String htmlContent = """
                <p>Dear <strong>%s</strong>,</p>

                <p>Your quotation for Purchase Request <strong>#%d</strong> has been <strong>%s</strong>.</p>

                <p>%s</p>

                <p>
                Best regards,<br>
                Synergy Shipbuilders
                </p>
                """.formatted(
                    vendorName,
                    prId,
                    status.toLowerCase(),
                    additionalInfo
                );

            // Apply the responsive template
            String emailContent = createResponsiveEmailTemplate(htmlContent);

            helper.setText(emailContent, true);
            System.out.println("Sending quotation status email to: " + toEmail);
            try {
                mailSender.send(message);
                System.out.println("Quotation status email sent successfully to: " + toEmail);
            } catch (Exception e) {
                System.err.println("Error sending quotation status email to " + toEmail + ": " + e.getMessage());
                e.printStackTrace();
                throw e;
            }
        } catch (MessagingException e) {
            System.err.println("MessagingException in sendQuotationStatusEmail: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to send quotation status email", e);
        }
    }

    public void sendQuantityChangedEmail(String toEmail, String vendorName, Long prId, String formUrl) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);

            helper.setTo(toEmail);
            helper.setFrom(fromEmail);
            helper.setSubject("Quantity Changed - New Quotation Required - PR#" + prId);

            String htmlContent = """
                <p>Dear <strong>%s</strong>,</p>

                <p>The quantities for Purchase Request <strong>#%d</strong> have been changed by our engineering department.</p>

                <p>Please submit a new quotation based on the updated quantities by clicking the button below:</p>

                <p style="text-align: center;">
                    <a href="%s" style="display: inline-block; padding: 10px 20px; margin: 15px 0; background-color: #0066cc; color: #ffffff; text-decoration: none; border-radius: 4px; font-weight: bold;">Submit Updated Quotation</a>
                </p>

                <p>If the button above doesn't work, you can copy and paste this link into your browser:</p>
                <p style="word-break: break-all; font-size: 12px;">%s</p>

                <p><strong>Note:</strong> Your previous quotation is no longer valid due to these quantity changes.</p>

                <p>
                Best regards,<br>
                Synergy Shipbuilders
                </p>
                """.formatted(vendorName, prId, formUrl, formUrl);

            // Apply the responsive template
            String emailContent = createResponsiveEmailTemplate(htmlContent);

            helper.setText(emailContent, true);
            System.out.println("Sending quotation request email to: " + toEmail);
            try {
                mailSender.send(message);
                System.out.println("Quotation request email sent successfully to: " + toEmail);
            } catch (Exception e) {
                System.err.println("Error sending quotation request email to " + toEmail + ": " + e.getMessage());
                e.printStackTrace();
                throw e;
            }
        } catch (MessagingException e) {
            System.err.println("MessagingException in sendQuotationRequestEmail: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to send quotation request email", e);
        }
    }

    /**
     * Sends quantity change notification email only if there are actual changes
     * @return boolean indicating if email was sent
     */
    public boolean sendQuantityChangedEmailIfNeeded(String toEmail, String vendorName, Long prId,
            String formUrl, boolean hasQuantityChanges, Integer changeInQuantity) {

        // Only send if there are actual changes
        if (!hasQuantityChanges || changeInQuantity == null || changeInQuantity == 0) {
            System.out.println("Skipping quantity change email - No actual changes detected for PR#" + prId);
            return false;
        }

        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);

            helper.setTo(toEmail);
            helper.setFrom(fromEmail);
            helper.setSubject("Quantity Changed - New Quotation Required - PR#" + prId);

            String htmlContent = """
                <p>Dear <strong>%s</strong>,</p>

                <p>The quantities for Purchase Request <strong>#%d</strong> have been changed by our engineering department.</p>
                <p><strong>Change in quantity:</strong> %d</p>

                <p>Please submit a new quotation based on the updated quantities by clicking the button below:</p>

                <p style="text-align: center;">
                    <a href="%s" style="display: inline-block; padding: 10px 20px; margin: 15px 0; background-color: #0066cc; color: #ffffff; text-decoration: none; border-radius: 4px; font-weight: bold;">Submit Updated Quotation</a>
                </p>

                <p>If the button above doesn't work, you can copy and paste this link into your browser:</p>
                <p style="word-break: break-all; font-size: 12px;">%s</p>

                <p><strong>Note:</strong> Your previous quotation is no longer valid due to these quantity changes.</p>

                <p>
                Best regards,<br>
                Synergy Shipbuilders
                </p>
                """.formatted(vendorName, prId, changeInQuantity, formUrl, formUrl);

            // Apply the responsive template
            String emailContent = createResponsiveEmailTemplate(htmlContent);

            helper.setText(emailContent, true);
            System.out.println("Sending quantity changed email (if needed) to: " + toEmail);
            try {
                mailSender.send(message);
                System.out.println("Quantity changed email (if needed) sent successfully to: " + toEmail + " for PR#" + prId);
                return true;
            } catch (Exception e) {
                System.err.println("Error sending quantity changed email (if needed) to " + toEmail + ": " + e.getMessage());
                e.printStackTrace();
                throw e;
            }
        } catch (MessagingException e) {
            System.err.println("MessagingException in sendQuantityChangedEmailIfNeeded: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to send quantity changed email (if needed)", e);
        }
    }
}
