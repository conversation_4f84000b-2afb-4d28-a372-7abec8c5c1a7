package com.synergy.service;

import com.synergy.dto.GRNFormDTO;
import com.synergy.dto.VendorSplitPODTO;
import com.synergy.dto.ApprovedPOLineItemDTO;
import com.synergy.entity.ShipbuildersItemEntity;
import com.synergy.entity.GRNEntity;
import com.synergy.entity.GRNLineItemEntity;
import com.synergy.repository.ShipbuildersItemRepository;
import com.synergy.repository.GRNRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Date;

@Service
public class GRNService {

    @Autowired
    private PurchaseRequestService purchaseRequestService;

    @Autowired
    private ShipbuildersItemRepository shipbuildersItemRepository;

    @Autowired
    private GRNRepository grnRepository;

    /**
     * Get GRN form data for a gate pass
     */
    public GRNFormDTO getGRNFormByGatePassId(String gatePassId) {
        // Extract vendor PO ID from gate pass ID (format: PO{vendorPoId}-GP{sequence})
        String vendorPoId = extractVendorPoIdFromGatePassId(gatePassId);
        
        // Get vendor PO details
        VendorSplitPODTO vendorPO = purchaseRequestService.getVendorPOByVendorPoId(vendorPoId);
        
        // Create GRN form DTO
        GRNFormDTO grnForm = new GRNFormDTO();
        grnForm.setGatePassId(gatePassId);
        grnForm.setVendorPoId(vendorPoId);
        grnForm.setYardNumber(vendorPO.getYardNumber());
        grnForm.setProjectName(vendorPO.getProjectName());
        grnForm.setVendorCompanyName(vendorPO.getVendorCompanyName());
        
        // Process line items and check for quality requirements
        List<GRNFormDTO.GRNLineItemDTO> grnLineItems = new ArrayList<>();
        boolean hasQualityCheck = false;
        
        if (vendorPO.getLineItems() != null) {
            for (ApprovedPOLineItemDTO item : vendorPO.getLineItems()) {
                GRNFormDTO.GRNLineItemDTO grnItem = new GRNFormDTO.GRNLineItemDTO();
                grnItem.setItemCode(item.getItemCode());
                grnItem.setDescription(item.getDescription());
                grnItem.setMaterialFamily(item.getMaterialFamily());
                grnItem.setQtyOrdered(item.getQuantity());
                grnItem.setQtyReceived(0); // Default to 0, will be filled by user
                grnItem.setStatus("Pending"); // Default status
                grnItem.setLocation("Yard"); // Default location
                grnItem.setRemarks("Enter Remarks"); // Default remarks
                grnItem.setUniqueCode(item.getUniqueCode());
                
                // Get quality check requirement from shipbuilders item
                String qualityCheckRequirement = getQualityCheckRequirement(item.getUniqueCode());
                grnItem.setQualityCheckRequirement(qualityCheckRequirement);
                
                // Check if this item requires quality check
                if (qualityCheckRequirement != null && 
                    !qualityCheckRequirement.equalsIgnoreCase("not required")) {
                    hasQualityCheck = true;
                }
                
                grnLineItems.add(grnItem);
            }
        }
        
        grnForm.setLineItems(grnLineItems);
        grnForm.setQualityCheck(hasQualityCheck);
        
        return grnForm;
    }
    
    /**
     * Extract vendor PO ID from gate pass ID
     */
    private String extractVendorPoIdFromGatePassId(String gatePassId) {
        // Gate pass ID format: PO{vendorPoId}-GP{sequence}
        // Example: PO647-1-GP01 -> 647-1
        if (gatePassId.startsWith("PO") && gatePassId.contains("-GP")) {
            String withoutPO = gatePassId.substring(2); // Remove "PO"
            int gpIndex = withoutPO.lastIndexOf("-GP");
            if (gpIndex > 0) {
                return withoutPO.substring(0, gpIndex);
            }
        }
        throw new IllegalArgumentException("Invalid gate pass ID format: " + gatePassId);
    }
    
    /**
     * Get quality check requirement for an item by unique code
     * Note: uniqueCode corresponds to itemCode in ShipbuildersItemEntity
     */
    private String getQualityCheckRequirement(String uniqueCode) {
        try {
            if (uniqueCode == null || uniqueCode.trim().isEmpty()) {
                return "not required";
            }

            // uniqueCode corresponds to itemCode in ShipbuildersItemEntity
            Optional<ShipbuildersItemEntity> itemOpt = shipbuildersItemRepository.findByItemCode(uniqueCode);
            if (itemOpt.isPresent()) {
                String qualityCheck = itemOpt.get().getQualityCheck();
                return qualityCheck != null ? qualityCheck : "not required";
            }
        } catch (Exception e) {
            System.out.println("Error getting quality check for unique code " + uniqueCode + ": " + e.getMessage());
        }
        return "not required"; // Default fallback
    }

    /**
     * Submit GRN form data
     */
    public GRNEntity submitGRN(GRNFormDTO grnFormDTO) {
        // Check if GRN already exists for this gate pass
        if (grnRepository.existsByGatePassId(grnFormDTO.getGatePassId())) {
            throw new IllegalArgumentException("GRN already exists for gate pass: " + grnFormDTO.getGatePassId());
        }

        // Create GRN entity
        GRNEntity grn = new GRNEntity();
        grn.setGrnNumber(generateGrnNumber());
        grn.setGatePassId(grnFormDTO.getGatePassId());
        grn.setVendorPoId(grnFormDTO.getVendorPoId());
        grn.setYardNumber(grnFormDTO.getYardNumber());
        grn.setProjectName(grnFormDTO.getProjectName());
        grn.setVendorCompanyName(grnFormDTO.getVendorCompanyName());
        grn.setQualityCheck(grnFormDTO.getQualityCheck());
        grn.setStatus("SUBMITTED");
        grn.setSubmittedDate(new Date());

        // Create line items
        List<GRNLineItemEntity> lineItems = new ArrayList<>();
        if (grnFormDTO.getLineItems() != null) {
            for (GRNFormDTO.GRNLineItemDTO itemDTO : grnFormDTO.getLineItems()) {
                GRNLineItemEntity lineItem = new GRNLineItemEntity();
                lineItem.setGrn(grn);
                lineItem.setItemCode(itemDTO.getItemCode());
                lineItem.setDescription(itemDTO.getDescription());
                lineItem.setMaterialFamily(itemDTO.getMaterialFamily());
                lineItem.setQtyOrdered(itemDTO.getQtyOrdered());
                lineItem.setQtyReceived(itemDTO.getQtyReceived());
                lineItem.setStatus(itemDTO.getStatus());
                lineItem.setLocation(itemDTO.getLocation());
                lineItem.setRemarks(itemDTO.getRemarks());
                lineItem.setQualityCheckRequirement(itemDTO.getQualityCheckRequirement());
                lineItem.setUniqueCode(itemDTO.getUniqueCode());
                lineItems.add(lineItem);
            }
        }
        grn.setLineItems(lineItems);

        // Save GRN
        return grnRepository.save(grn);
    }

    /**
     * Update GRN
     */
    public GRNEntity updateGRN(Long grnId, GRNFormDTO grnFormDTO) {
        Optional<GRNEntity> existingGrnOpt = grnRepository.findById(grnId);
        if (existingGrnOpt.isEmpty()) {
            throw new IllegalArgumentException("GRN not found with ID: " + grnId);
        }

        GRNEntity grn = existingGrnOpt.get();

        // Only allow updates if status is DRAFT or SUBMITTED
        if (!"DRAFT".equals(grn.getStatus()) && !"SUBMITTED".equals(grn.getStatus())) {
            throw new IllegalArgumentException("Cannot update GRN with status: " + grn.getStatus());
        }

        // Update GRN fields
        grn.setYardNumber(grnFormDTO.getYardNumber());
        grn.setProjectName(grnFormDTO.getProjectName());
        grn.setVendorCompanyName(grnFormDTO.getVendorCompanyName());
        grn.setQualityCheck(grnFormDTO.getQualityCheck());

        // Clear existing line items and add new ones
        grn.getLineItems().clear();
        if (grnFormDTO.getLineItems() != null) {
            for (GRNFormDTO.GRNLineItemDTO itemDTO : grnFormDTO.getLineItems()) {
                GRNLineItemEntity lineItem = new GRNLineItemEntity();
                lineItem.setGrn(grn);
                lineItem.setItemCode(itemDTO.getItemCode());
                lineItem.setDescription(itemDTO.getDescription());
                lineItem.setMaterialFamily(itemDTO.getMaterialFamily());
                lineItem.setQtyOrdered(itemDTO.getQtyOrdered());
                lineItem.setQtyReceived(itemDTO.getQtyReceived());
                lineItem.setStatus(itemDTO.getStatus());
                lineItem.setLocation(itemDTO.getLocation());
                lineItem.setRemarks(itemDTO.getRemarks());
                lineItem.setQualityCheckRequirement(itemDTO.getQualityCheckRequirement());
                lineItem.setUniqueCode(itemDTO.getUniqueCode());
                grn.getLineItems().add(lineItem);
            }
        }

        return grnRepository.save(grn);
    }

    /**
     * Delete GRN
     */
    public void deleteGRN(Long grnId) {
        Optional<GRNEntity> existingGrnOpt = grnRepository.findById(grnId);
        if (existingGrnOpt.isEmpty()) {
            throw new IllegalArgumentException("GRN not found with ID: " + grnId);
        }

        GRNEntity grn = existingGrnOpt.get();

        // Only allow deletion if status is DRAFT
        if (!"DRAFT".equals(grn.getStatus())) {
            throw new IllegalArgumentException("Cannot delete GRN with status: " + grn.getStatus());
        }

        grnRepository.deleteById(grnId);
    }

    /**
     * Generate GRN number
     */
    private String generateGrnNumber() {
        Long sequence = grnRepository.getNextGrnSequence();
        return String.format("GRN%04d", sequence);
    }
}
