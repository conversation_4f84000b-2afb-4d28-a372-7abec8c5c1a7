package com.synergy.service;

import com.synergy.dto.AddressDTO;
import com.synergy.dto.VendorDTO;
import com.synergy.entity.PurchaseRequestEntity;
import com.synergy.entity.VendorEntity;
import com.synergy.entity.VendorBiddingTokenEntity;
import com.synergy.repository.PurchaseRequestRepository;
import com.synergy.repository.VendorRepository;
import com.synergy.repository.VendorBiddingTokenRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class VendorService {

	@Autowired
	private VendorRepository vendorRepository;

	@Autowired
	private PurchaseRequestRepository purchaseRequestRepository;

	@Autowired
	private VendorBiddingTokenRepository vendorBiddingTokenRepository;

	private final ObjectMapper objectMapper = new ObjectMapper();

	public List<VendorDTO> getAllVendors() {
		return vendorRepository.findAll().stream().map(this::convertToDTO).collect(Collectors.toList());
	}

	public VendorDTO getVendorById(Long id) {
		Optional<VendorEntity> vendorOptional = vendorRepository.findById(id);
		if (vendorOptional.isPresent()) {
			return convertToDTO(vendorOptional.get());
		} else {
			throw new IllegalArgumentException("Vendor not found with ID: " + id);
		}
	}

	@Transactional
	public VendorDTO createVendor(VendorDTO vendorDTO) throws JsonProcessingException {
		VendorEntity entity = convertToEntity(vendorDTO);
		VendorEntity savedEntity = vendorRepository.save(entity);
		return convertToDTO(savedEntity);
	}

	@Transactional
	public VendorDTO updateVendor(Long id, VendorDTO vendorDTO) throws JsonProcessingException {
		Optional<VendorEntity> vendorOptional = vendorRepository.findById(id);
		if (vendorOptional.isPresent()) {
			// Update the entity with DTO values
			VendorEntity updatedEntity = convertToEntity(vendorDTO);
			updatedEntity.setSrNo(id); // Ensure ID is preserved

			VendorEntity savedEntity = vendorRepository.save(updatedEntity);
			return convertToDTO(savedEntity);
		} else {
			throw new IllegalArgumentException("Vendor not found with ID: " + id);
		}
	}

	@Transactional
	public void deleteVendor(Long srNo) {
		VendorEntity vendor = vendorRepository.findById(srNo)
				.orElseThrow(() -> new IllegalArgumentException("Vendor not found"));

		// Delete all bidding tokens for this vendor first
		List<VendorBiddingTokenEntity> biddingTokens = vendorBiddingTokenRepository.findByVendorSrNo(srNo);
		vendorBiddingTokenRepository.deleteAll(biddingTokens);

		// Remove vendor from purchase requests
		List<PurchaseRequestEntity> linkedRequests = purchaseRequestRepository.findByVendorsContaining(vendor);
		for (PurchaseRequestEntity pr : linkedRequests) {
			pr.getVendors().remove(vendor);
		}

		// Finally delete the vendor
		vendorRepository.delete(vendor);
	}

	private VendorDTO convertToDTO(VendorEntity entity) {
		VendorDTO dto = new VendorDTO();
		dto.setSrNo(entity.getSrNo());
		dto.setCompanyName(entity.getCompanyName());
		dto.setGstNumber(entity.getGstNumber());
		dto.setPanNo(entity.getPanNo());
		dto.setVendorName(entity.getVendorName());
		dto.setWhatsappNumber(entity.getWhatsappNumber());
		dto.setContactNumber(entity.getContactNumber());
		dto.setEmailId(entity.getEmailId());

		// Convert JSON string to List<AddressDTO> for addresses
		try {
			if (entity.getAddress() != null && !entity.getAddress().isEmpty()) {
				List<AddressDTO> addresses = objectMapper.readValue(entity.getAddress(),
						new TypeReference<List<AddressDTO>>() {
						});
				dto.setAddresses(addresses);
			}
		} catch (JsonProcessingException e) {
			// If there's an error parsing the JSON, set an empty list
			dto.setAddresses(List.of());
		}

		// Add country, city, state fields
		dto.setCountry(entity.getCountry());
		dto.setCity(entity.getCity());
		dto.setState(entity.getState());

		dto.setVendorCode(entity.getVendorCode());
		dto.setCreditDays(entity.getCreditDays());
		dto.setCreditLimit(entity.getCreditLimit());
		dto.setRemark(entity.getRemark());
		dto.setVendorType(entity.getVendorType());
		return dto;
	}

	private VendorEntity convertToEntity(VendorDTO dto) throws JsonProcessingException {
		VendorEntity entity = new VendorEntity();
		entity.setSrNo(dto.getSrNo());
		entity.setCompanyName(dto.getCompanyName());
		entity.setGstNumber(dto.getGstNumber());
		entity.setPanNo(dto.getPanNo());
		entity.setVendorName(dto.getVendorName());
		entity.setWhatsappNumber(dto.getWhatsappNumber());
		entity.setContactNumber(dto.getContactNumber());
		entity.setEmailId(dto.getEmailId());

		// Convert List<AddressDTO> to JSON string for addresses
		if (dto.getAddresses() != null && !dto.getAddresses().isEmpty()) {
			String addressesJson = objectMapper.writeValueAsString(dto.getAddresses());
			entity.setAddress(addressesJson);

			// Set the first address's fields to the entity fields for filtering
			AddressDTO firstAddress = dto.getAddresses().get(0);
			entity.setCountry(firstAddress.getCountry());
			entity.setCity(firstAddress.getCity());
			entity.setState(firstAddress.getState());

			// Only set vendor type from address if it's not already set in the DTO
			if (dto.getVendorType() == null && firstAddress.getType() != null) {
				entity.setVendorType(firstAddress.getType());
			}
		} else {
			// If no addresses, use the direct fields if provided
			entity.setCountry(dto.getCountry());
			entity.setCity(dto.getCity());
			entity.setState(dto.getState());
		}

		// Set vendor type from DTO if provided
		if (dto.getVendorType() != null) {
			entity.setVendorType(dto.getVendorType());
		}

		entity.setVendorCode(dto.getVendorCode());
		entity.setCreditDays(dto.getCreditDays());
		entity.setCreditLimit(dto.getCreditLimit());
		entity.setRemark(dto.getRemark());
		return entity;
	}
}
