package com.synergy.service;

import com.synergy.dto.ShipbuildersItemDropdownDTO;
import com.synergy.entity.ShipbuildersItemEntity;
import com.synergy.repository.ShipbuildersItemRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Optional;


@Service
public class ShipbuildersItemService {

    private static final Logger logger = LoggerFactory.getLogger(ShipbuildersItemService.class);

    @Autowired
    private ShipbuildersItemRepository shipbuildersItemRepository;

    @Cacheable("itemsList")
    public List<ShipbuildersItemEntity> getAllItems() {
        return shipbuildersItemRepository.findAll();
    }

    @Cacheable("itemsDropdown")
    public List<ShipbuildersItemDropdownDTO> getAllItemsForDropdown() {
        return shipbuildersItemRepository.findAllForDropdown();
    }

    @Cacheable(value = "itemById", key = "#id")
    public Optional<ShipbuildersItemEntity> getItemById(Long id) {
        logger.info("Fetching item with ID: {} from repository or cache", id);
        Optional<ShipbuildersItemEntity> item = shipbuildersItemRepository.findById(id);
        if (item.isPresent()) {
            logger.info("Found item with ID: {}, name: {}", id, item.get().getItemName());
        } else {
            logger.warn("Item with ID: {} not found in repository", id);
        }
        return item;
    }

    @Cacheable(value = "itemsBySubCategory", key = "#subCategory")
    public List<ShipbuildersItemEntity> findBySubCategory(String subCategory) {
        return shipbuildersItemRepository.findBySubCategoryContainingIgnoreCase(subCategory);
    }

    @Cacheable(value = "itemsSearch", key = "#subCategory + '_' + #description + '_' + #itemCode")
    public List<ShipbuildersItemEntity> searchItems(String subCategory, String description, String itemCode) {
        return shipbuildersItemRepository.searchItems(subCategory, description, itemCode);
    }

    @CacheEvict(value = {"itemsList", "itemsDropdown", "itemById", "itemsBySubCategory", "itemsSearch"}, allEntries = true)
    public ShipbuildersItemEntity saveItem(ShipbuildersItemEntity item) {
        logger.info("Saving item with ID: {}, name: {}", item.getId(), item.getItemName());

        // Auto-generate itemCode if not provided
        if (item.getItemCode() == null || item.getItemCode().trim().isEmpty()) {
            long count = shipbuildersItemRepository.count();
            String generatedCode = item.getCategory().substring(0, 3).toUpperCase() + item.getSubCategory().substring(0, 3).toUpperCase() + String.format("%03d", count + 1);
            item.setItemCode(generatedCode);
            logger.info("Auto-generated item code: {}", generatedCode);
        }

        // Save the item
        ShipbuildersItemEntity savedItem = shipbuildersItemRepository.save(item);

        if (item.getId() != null) {
            logger.info("Evicting item with ID: {} from cache", item.getId());
            clearItemCache(item.getId());
        }

        logger.info("Item saved successfully. ID: {}, name: {}", savedItem.getId(), savedItem.getItemName());
        return savedItem;
    }

    @CacheEvict(value = {"itemsList"}, allEntries = true)
    public void deleteById(Long id) {
        shipbuildersItemRepository.deleteById(id);
    }

    /**
     * Explicitly clear the cache for a specific item ID
     */
    @CacheEvict(value = "itemById", key = "#id")
    public void clearItemCache(Long id) {
        logger.info("Explicitly clearing cache for item ID: {}", id);
    }

}
