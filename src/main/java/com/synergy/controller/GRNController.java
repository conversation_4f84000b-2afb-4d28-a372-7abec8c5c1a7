package com.synergy.controller;

import com.synergy.dto.GRNFormDTO;
import com.synergy.entity.GRNEntity;
import com.synergy.service.GRNService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/grn")
@CrossOrigin(origins = "*", allowedHeaders = "*", methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE, RequestMethod.OPTIONS})
@Tag(name = "GRN Management", description = "APIs for managing Goods Received Notes (GRN)")
public class GRNController {

    @Autowired
    private GRNService grnService;

    /**
     * Get GRN form data for a specific gate pass
     */
    @GetMapping("/form/{gatePassId}")
    @Operation(
        summary = "Get GRN form data",
        description = "Retrieves GRN form data for a specific gate pass ID. " +
                "This includes line items with quality check requirements. " +
                "If any item has quality_check field set to anything other than 'not required', " +
                "the qualityCheck flag will be set to true and quantity received field should be uneditable.",
        tags = {"GRN Management"}
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "GRN form data retrieved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = GRNFormDTO.class))
        ),
        @ApiResponse(responseCode = "400", description = "Invalid gate pass ID"),
        @ApiResponse(responseCode = "404", description = "Gate pass not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getGRNForm(
            @Parameter(description = "Gate Pass ID", required = true, example = "PO647-GP01")
            @PathVariable String gatePassId) {
        try {
            GRNFormDTO grnForm = grnService.getGRNFormByGatePassId(gatePassId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", grnForm);
            response.put("message", "GRN form data retrieved successfully");

            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Invalid gate pass ID: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to retrieve GRN form data: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Submit GRN form data
     */
    @PostMapping("/submit")
    @Operation(
        summary = "Submit GRN form",
        description = "Submits GRN form data and creates a GRN record in the system",
        tags = {"GRN Management"}
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "GRN submitted successfully",
            content = @Content(mediaType = "application/json")
        ),
        @ApiResponse(responseCode = "400", description = "Invalid input or GRN already exists"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> submitGRN(@RequestBody GRNFormDTO grnFormDTO) {
        try {
            GRNEntity createdGRN = grnService.submitGRN(grnFormDTO);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "GRN submitted successfully");
            response.put("data", Map.of(
                "grnNumber", createdGRN.getGrnNumber(),
                "grnId", createdGRN.getId(),
                "status", createdGRN.getStatus(),
                "submittedDate", createdGRN.getSubmittedDate()
            ));

            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to submit GRN: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Update GRN
     */
    @PutMapping("/{grnId}")
    @Operation(
        summary = "Update GRN",
        description = "Updates an existing GRN by ID. Only allowed for DRAFT or SUBMITTED status.",
        tags = {"GRN Management"}
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "GRN updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input or GRN cannot be updated"),
        @ApiResponse(responseCode = "404", description = "GRN not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> updateGRN(
            @Parameter(description = "GRN ID", required = true) @PathVariable Long grnId,
            @RequestBody GRNFormDTO grnFormDTO) {
        try {
            GRNEntity updatedGRN = grnService.updateGRN(grnId, grnFormDTO);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "GRN updated successfully");
            response.put("data", Map.of(
                "grnNumber", updatedGRN.getGrnNumber(),
                "grnId", updatedGRN.getId(),
                "status", updatedGRN.getStatus(),
                "updatedDate", updatedGRN.getUpdatedDate()
            ));

            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to update GRN: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Delete GRN
     */
    @DeleteMapping("/{grnId}")
    @Operation(
        summary = "Delete GRN",
        description = "Deletes a GRN by ID. Only allowed for DRAFT status.",
        tags = {"GRN Management"}
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "GRN deleted successfully"),
        @ApiResponse(responseCode = "400", description = "GRN cannot be deleted"),
        @ApiResponse(responseCode = "404", description = "GRN not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> deleteGRN(
            @Parameter(description = "GRN ID", required = true) @PathVariable Long grnId) {
        try {
            grnService.deleteGRN(grnId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "GRN deleted successfully");

            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to delete GRN: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
