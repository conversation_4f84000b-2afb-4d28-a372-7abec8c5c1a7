package com.synergy.controller;

import com.synergy.dto.ApproveRejectRequestDTO;
import com.synergy.dto.ApprovedPODTO;
import com.synergy.dto.PaymentRevisionDTO;
import com.synergy.dto.PurchaseOrderDeliveryTermsDTO;
import com.synergy.dto.PurchaseRequestDTO;
import com.synergy.dto.PurchaseRequestLightDTO;
import com.synergy.dto.VendorRfqStatusDTO;
import com.synergy.dto.VendorSplitPODTO;
import com.synergy.entity.PurchaseRequestEntity;
import com.synergy.service.PurchaseRequestService;
import com.synergy.service.ExportService;
import com.synergy.service.VendorPODeliveryTermsService;
import com.synergy.repository.PurchaseRequestRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;

@RestController
@RequestMapping("/api/purchase-requests")
@CrossOrigin(origins = "*", allowedHeaders = "*", methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE, RequestMethod.OPTIONS})
@Tag(name = "Purchase Request Management", description = "APIs for managing purchase requests")
@Tag(name = "Purchase Request Workflow", description = "APIs for the purchase request workflow including Internal, Validation, and Management processes")
public class PurchaseRequestController {

    @Autowired
    private PurchaseRequestService purchaseRequestService;

    @Autowired
    private ExportService exportService;

    @Autowired
    private VendorPODeliveryTermsService deliveryTermsService;

    @Autowired
    private PurchaseRequestRepository purchaseRequestRepository;

    @GetMapping
    public ResponseEntity<List<PurchaseRequestDTO>> getAllPurchaseRequests() {
        return ResponseEntity.ok(purchaseRequestService.getAllPurchaseRequests());
    }

    @GetMapping("/{prId}")
    public ResponseEntity<PurchaseRequestDTO> getPurchaseRequestById(@PathVariable Long prId) {
        return ResponseEntity.ok(purchaseRequestService.getPurchaseRequestById(prId));
    }

    @PostMapping
    public ResponseEntity<?> createPurchaseRequest(@RequestBody PurchaseRequestDTO purchaseRequestDTO) {
        try {
            PurchaseRequestDTO createdPR = purchaseRequestService.createPurchaseRequest(purchaseRequestDTO);
            return ResponseEntity.ok(createdPR);
        } catch (IllegalArgumentException e) {
            // Handle validation errors (like duplicate line items) with 400 Bad Request
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            // Handle other unexpected errors with 500 Internal Server Error
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", "Failed to create purchase request: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @PutMapping("/{prId}")
    public ResponseEntity<?> updatePurchaseRequest(
            @PathVariable Long prId,
            @RequestBody PurchaseRequestDTO dto) {
        try {
            PurchaseRequestDTO updatedPR = purchaseRequestService.updatePurchaseRequest(prId, dto);
            return ResponseEntity.ok(updatedPR);
        } catch (IllegalArgumentException e) {
            // Handle validation errors (like duplicate line items) with 400 Bad Request
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            // Handle other unexpected errors with 500 Internal Server Error
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", "Failed to update purchase request: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @DeleteMapping("/{prId}")
    public ResponseEntity<Void> deletePurchaseRequest(@PathVariable Long prId) {
        purchaseRequestService.deletePurchaseRequest(prId);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/by-status")
    @Operation(
        summary = "Get purchase requests by status",
        description = "Retrieves a list of purchase requests filtered by status (e.g., APPROVED, REJECTED, PENDING_APPROVAL). " +
                "For rejected requests, the rejection reason is included in the response."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Purchase requests retrieved successfully"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getPurchaseRequestsByStatus(
            @Parameter(description = "Status to filter by (e.g., APPROVED, REJECTED, PENDING_APPROVAL)", required = true,
                    example = "APPROVED")
            @RequestParam String status) {
        try {
            List<PurchaseRequestDTO> purchaseRequests = purchaseRequestService.getAllPurchaseRequestsByStatus(status);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", purchaseRequests);
            response.put("count", purchaseRequests.size());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/by-date-range")
    @Operation(
        summary = "Get purchase requests by date range with optional filtering",
        description = "Retrieves purchase requests filtered by date range (fromDate to toDate). " +
                "Supports optional status, prType filtering and section-specific responses. " +
                "Section parameter determines the response format: " +
                "'rfq-comparison' - Returns light DTOs with RFQ status for RFQ comparison workflow, " +
                "'creation' - Returns light DTOs for PR creation workflow, " +
                "'manager' - Returns light DTOs for manager workflow, " +
                "'in-progress-management' - Returns light DTOs for in-progress management workflow, " +
                "'internal' - Returns full DTOs for internal workflow, " +
                "No section or unknown section - Returns full DTOs with complete information. " +
                "Date format: yyyy-MM-dd (e.g., 2024-01-15)"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Purchase requests retrieved successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid date range or parameters"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getPurchaseRequestsByDateRange(
            @Parameter(description = "Start date for filtering (yyyy-MM-dd)", required = true, example = "2024-01-01")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date fromDate,
            @Parameter(description = "End date for filtering (yyyy-MM-dd)", required = true, example = "2024-01-31")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date toDate,
            @Parameter(description = "Optional status filter (e.g., PENDING_APPROVAL, APPROVED, REJECTED)", required = false)
            @RequestParam(required = false) String status,
            @Parameter(description = "Optional prType filter (REGULAR, INSTANT)", required = false)
            @RequestParam(required = false) String prType,
            @Parameter(description = "Optional section filter for specific workflows (rfq-comparison, creation, manager, in-progress-management, internal)", required = false)
            @RequestParam(required = false) String section) {
        try {
            List<?> purchaseRequests = purchaseRequestService.getPurchaseRequestsByDateRange(fromDate, toDate, status, prType, section);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", purchaseRequests);
            response.put("count", purchaseRequests.size());
            response.put("dateRange", Map.of(
                "fromDate", fromDate,
                "toDate", toDate
            ));
            if (status != null && !status.trim().isEmpty()) {
                response.put("status", status);
            }
            if (prType != null && !prType.trim().isEmpty()) {
                response.put("prType", prType);
            }
            if (section != null && !section.trim().isEmpty()) {
                response.put("section", section);
            }
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @PostMapping("/{prId}/send-for-approval")
    @Operation(
        summary = "Send a purchase request for approval",
        description = "Changes the status of a purchase request to PENDING_APPROVAL or PENDING_QUANTITY_APPROVAL " +
                "based on whether there are quantity changes."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Purchase request sent for approval successfully"),
        @ApiResponse(responseCode = "400", description = "Purchase request not in valid state for approval"),
        @ApiResponse(responseCode = "404", description = "Purchase request not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> sendForApproval(
            @Parameter(description = "ID of the purchase request to send for approval")
            @PathVariable Long prId) {
        try {
            PurchaseRequestDTO updatedPR = purchaseRequestService.sendForApproval(prId);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", updatedPR);
            response.put("message", "Purchase Request sent for approval successfully");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException | IllegalStateException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (RuntimeException e) {
            if (e.getMessage().contains("not found")) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", e.getMessage());
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
            throw e;
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @PostMapping("/{prId}/send-back-to-vendors")
    @Operation(
        summary = "Send a purchase request back to vendors with updated quantities",
        description = "Changes the status of a purchase request to PENDING_VENDOR_SELECTION and sends emails to vendors " +
                "with updated quantities. This is used when there are quantity changes in the Internal workflow.",
        tags = {"Purchase Request Workflow"}
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Purchase request sent back to vendors successfully"),
        @ApiResponse(responseCode = "400", description = "Purchase request not in valid state"),
        @ApiResponse(responseCode = "404", description = "Purchase request not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> sendBackToVendors(
            @Parameter(description = "ID of the purchase request to send back to vendors")
            @PathVariable Long prId) {
        try {
            PurchaseRequestDTO updatedPR = purchaseRequestService.sendBackToVendors(prId);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", updatedPR);
            response.put("message", "Purchase Request sent back to vendors with updated quantities successfully");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException | IllegalStateException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (RuntimeException e) {
            if (e.getMessage().contains("not found")) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", e.getMessage());
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
            throw e;
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @PostMapping("/{prId}/approve-reject")
    @Operation(
        summary = "Approve or reject a purchase request",
        description = "Approves or rejects a purchase request based on the action provided. " +
                "If rejecting, a rejection reason must be provided. " +
                "For PENDING_QUANTITY_APPROVAL, approval moves it to PENDING_APPROVAL. " +
                "For REGULAR PRs with quantity changes, approval moves it to IN_PROGRESS_MANAGEMENT. " +
                "For IN_PROGRESS_MANAGEMENT, approval moves it to APPROVED.",
        tags = {"Purchase Request Workflow"}
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Purchase request approved/rejected successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input or purchase request not in valid state"),
        @ApiResponse(responseCode = "404", description = "Purchase request not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> approveRejectPurchaseRequest(
            @Parameter(description = "ID of the purchase request to approve/reject")
            @PathVariable Long prId,
            @Parameter(description = "Approval/rejection details", required = true)
            @RequestBody ApproveRejectRequestDTO requestDTO) {
        try {
            PurchaseRequestDTO updatedPR = purchaseRequestService.approveRejectPurchaseRequest(
                    prId,
                    requestDTO.getAction(),
                    requestDTO.getRejectionReason()
            );

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", updatedPR);
            response.put("message", "APPROVE".equals(requestDTO.getAction()) ?
                    "Purchase Request approved successfully" :
                    "Purchase Request rejected successfully");

            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException | IllegalStateException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (RuntimeException e) {
            if (e.getMessage().contains("not found")) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", e.getMessage());
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
            throw e;
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }


    @GetMapping("/export/pdf")
    public void exportAllToPdf(HttpServletResponse response) {
        try {
            List<PurchaseRequestEntity> purchaseRequests = purchaseRequestRepository.findAllByOrderByCreatedDateDesc();
            exportService.exportPurchaseRequestsToPdf(purchaseRequests, response);
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/export/excel")
    public void exportAllToExcel(HttpServletResponse response) {
        try {
            List<PurchaseRequestEntity> purchaseRequests = purchaseRequestRepository.findAllByOrderByCreatedDateDesc();
            exportService.exportPurchaseRequestsToExcel(purchaseRequests, response);
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }
    @GetMapping("/export/regular/excel")
    public void exportRegularPRsToExcel(HttpServletResponse response){
        try {
            List<PurchaseRequestEntity> regularPRs = purchaseRequestRepository.findByPrTypeIgnoreCase("REGULAR");
            exportService.exportRegularPRsToExcel(regularPRs, response);
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/export/regular/pdf")
    public void exportRegularPRsToPdf(HttpServletResponse response) {
        try {
        List<PurchaseRequestEntity> regularPRs = purchaseRequestRepository.findByPrTypeIgnoreCase("REGULAR");
        exportService.exportRegularPRsToPdf(regularPRs, response);
    } catch (Exception e) {
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
    }
    }

    @GetMapping("/pdf/status/{status}")
    public void exportPRsToPdfByStatus(@PathVariable String status, HttpServletResponse response) throws Exception {
        List<PurchaseRequestEntity> list = purchaseRequestRepository.findByStatusIgnoreCase(status);
        if (list.isEmpty()) {
            throw new RuntimeException("No Purchase Requests found with status: " + status);
        }
        exportService.exportRegularPRsToPdf(list, response);
    }

    @GetMapping("/excel/status/{status}")
    public void exportPRsToExcelByStatus(@PathVariable String status, HttpServletResponse response) throws Exception {
        List<PurchaseRequestEntity> list = purchaseRequestRepository.findByStatusIgnoreCase(status);
        if (list.isEmpty()) {
            throw new RuntimeException("No Purchase Requests found with status: " + status);
        }
        exportService.exportRegularPRsToExcel(list, response);
    }

    @GetMapping("/for-creation")
    @Operation(
        summary = "Get purchase requests for PR Creation workflow",
        description = "Retrieves purchase requests with status 'PENDING_APPROVAL' OR 'PENDING_VENDOR_SELECTION'. " +
                "Returns only essential fields (ID, Date, Client Name, Yard Number, Project Name, Line Items count, Status, Remarks) " +
                "to optimize performance and reduce loading time. " +
                "This ultra-optimized endpoint is specifically designed for the PR Creation workflow."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Purchase requests retrieved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = PurchaseRequestLightDTO.class))
        ),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getPurchaseRequestsForCreation() {
        try {
            List<PurchaseRequestLightDTO> purchaseRequests = purchaseRequestService.getPurchaseRequestsForCreation();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", purchaseRequests);
            response.put("count", purchaseRequests.size());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/for-rfq-comparison")
    @Operation(
        summary = "Get purchase requests for RFQ Comparison workflow",
        description = "Retrieves purchase requests with status 'PENDING_APPROVAL' (for both Regular and Instant PR types) " +
                "where vendors have been selected. Returns only essential fields (ID, Date, Client Name, Yard Number, " +
                "Project Name, Line Items count, Status, RFQ Status, Remarks) to optimize performance and reduce loading time. " +
                "This ultra-optimized endpoint is specifically designed for the RFQ Comparison dashboard view."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Purchase requests retrieved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = PurchaseRequestLightDTO.class))
        ),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getPurchaseRequestsForRfqComparison() {
        try {
            List<PurchaseRequestLightDTO> purchaseRequests = purchaseRequestService.getPurchaseRequestsForRfqComparison();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", purchaseRequests);
            response.put("count", purchaseRequests.size());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/for-manager")
    @Operation(
        summary = "Get purchase requests for Manager In-Progress workflow",
        description = "Retrieves purchase requests with status 'PENDING_APPROVAL' where vendors have been selected. " +
                "Returns only essential fields (ID, Date, Client Name, Yard Number, Project Name, Line Items count, Status) " +
                "to optimize performance and reduce loading time. " +
                "This ultra-optimized endpoint is specifically designed for the Manager In-Progress dashboard view."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Purchase requests retrieved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = PurchaseRequestLightDTO.class))
        ),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getPurchaseRequestsForManager() {
        try {
            List<PurchaseRequestLightDTO> purchaseRequests = purchaseRequestService.getPurchaseRequestsForManager();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", purchaseRequests);
            response.put("count", purchaseRequests.size());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/for-internal")
    @Operation(
        summary = "Get purchase requests for Internal workflow",
        description = "Retrieves purchase requests with status 'PENDING_QUANTITY_APPROVAL' where quantity has been changed. " +
                "Returns complete data including line items with quantity changes. " +
                "This endpoint is specifically designed for the Internal part of the workflow where quantity changes need to be reviewed.",
        tags = {"Purchase Request Workflow"}
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Purchase requests retrieved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = PurchaseRequestDTO.class))
        ),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getPurchaseRequestsForInternal() {
        try {
            List<PurchaseRequestDTO> purchaseRequests = purchaseRequestService.getPurchaseRequestsForInternal();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", purchaseRequests);
            response.put("count", purchaseRequests.size());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/for-in-progress-management")
    @Operation(
        summary = "Get purchase requests for In-Progress Management workflow",
        description = "Retrieves purchase requests with status 'IN_PROGRESS_MANAGEMENT'. " +
                "Returns only essential fields (ID, Date, Client Name, Yard Number, Project Name, Line Items count, Status, Remarks) " +
                "to optimize performance and reduce loading time. " +
                "This endpoint is specifically designed for the Management In-Progress dashboard view for PRs that have been " +
                "approved from validation part and are now in progress in management.",
        tags = {"Purchase Request Workflow"}
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Purchase requests retrieved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = PurchaseRequestLightDTO.class))
        ),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getPurchaseRequestsForInProgressManagement() {
        try {
            List<PurchaseRequestLightDTO> purchaseRequests = purchaseRequestService.getPurchaseRequestsForInProgressManagement();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", purchaseRequests);
            response.put("count", purchaseRequests.size());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @PutMapping("/for-creation/{prId}")
    @Operation(
        summary = "Update a purchase request in the PR Creation workflow",
        description = "Updates a purchase request that is in the PENDING_VENDOR_SELECTION status. " +
                "This endpoint is specifically designed for the PR Creation workflow to update vendor selections " +
                "and other details before sending for approval."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Purchase request updated successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = PurchaseRequestDTO.class))
        ),
        @ApiResponse(responseCode = "400", description = "Invalid input or purchase request not in valid state"),
        @ApiResponse(responseCode = "404", description = "Purchase request not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> updatePurchaseRequestForCreation(
            @Parameter(description = "ID of the purchase request to update", required = true, example = "1")
            @PathVariable Long prId,
            @Parameter(description = "Updated purchase request data", required = true)
            @RequestBody PurchaseRequestDTO dto) {
        try {
            PurchaseRequestDTO updatedPR = purchaseRequestService.updatePurchaseRequestForCreation(prId, dto);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", updatedPR);
            response.put("message", "Purchase Request updated successfully");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException | IllegalStateException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        } catch (RuntimeException e) {
            if (e.getMessage().contains("not found")) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", e.getMessage());
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
            throw e;
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/for-creation/{prId}")
    @Operation(
        summary = "Get detailed purchase request information for PR Creation form",
        description = "Retrieves detailed information for a specific purchase request including line items, vendors, and other details. " +
                "This endpoint is designed to be called when a user clicks on the action button in the PR Creation dashboard " +
                "to open the form with pre-filled data."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Purchase request details retrieved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = PurchaseRequestDTO.class))
        ),
        @ApiResponse(responseCode = "404", description = "Purchase request not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getPurchaseRequestDetailsForCreation(
            @Parameter(description = "ID of the purchase request to retrieve", required = true, example = "1")
            @PathVariable Long prId) {
        try {
            PurchaseRequestDTO purchaseRequest = purchaseRequestService.getPurchaseRequestById(prId);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", purchaseRequest);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/for-creation/export/pdf")
    public void exportForCreationToPdf(HttpServletResponse response) {
        try {
            List<PurchaseRequestLightDTO> prList = purchaseRequestService.getPurchaseRequestsForCreation();
            exportService.exportPurchaseRequestLightDTOsToPdf(prList, response);
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/for-creation/export/excel")
    public void exportForCreationToExcel(HttpServletResponse response) {
        try {
            List<PurchaseRequestLightDTO> prList = purchaseRequestService.getPurchaseRequestsForCreation();
            exportService.exportPurchaseRequestLightDTOsToExcel(prList, response);
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/for-rfq-comparison/export/pdf")
    public void exportForRfqComparisonToPdf(HttpServletResponse response) {
        try {
            List<PurchaseRequestLightDTO> prList = purchaseRequestService.getPurchaseRequestsForRfqComparison();
            exportService.exportPurchaseRequestLightDTOsToPdf(prList, response);
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/for-rfq-comparison/export/excel")
    public void exportForRfqComparisonToExcel(HttpServletResponse response) {
        try {
            List<PurchaseRequestLightDTO> prList = purchaseRequestService.getPurchaseRequestsForRfqComparison();
            exportService.exportPurchaseRequestLightDTOsToExcel(prList, response);
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/approved/export/pdf")
    public void exportApprovedPRsToPdf(HttpServletResponse response) {
        try {
            List<PurchaseRequestLightDTO> prList = purchaseRequestService.getPurchaseRequestsApprovedLight();
            exportService.exportPurchaseRequestLightDTOsToPdf(prList, response);
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/approved/export/excel")
    public void exportApprovedPRsToExcel(HttpServletResponse response) {
        try {
            List<PurchaseRequestLightDTO> prList = purchaseRequestService.getPurchaseRequestsApprovedLight();
            exportService.exportPurchaseRequestLightDTOsToExcel(prList, response);
        } catch (Exception e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Generate a Purchase Order PDF for an approved purchase request
     *
     * @param prId The ID of the purchase request
     * @param deliveryTerms The delivery terms from the form
     * @param response The HTTP response
     */
    @PostMapping("/{prId}/generate-purchase-order")
    @Operation(
        summary = "Generate Purchase Order PDF",
        description = "Generates a PDF purchase order for an approved purchase request with the provided delivery terms"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "PDF generated successfully"),
        @ApiResponse(responseCode = "404", description = "Purchase request not found"),
        @ApiResponse(responseCode = "400", description = "Invalid input or purchase request not in approved status")
    })
    public ResponseEntity<?> generatePurchaseOrderPdf(
            @Parameter(description = "ID of the purchase request", required = true)
            @PathVariable Long prId,
            @RequestBody PurchaseOrderDeliveryTermsDTO deliveryTerms,
            HttpServletResponse response) {
        try {
            // Find the purchase request
            PurchaseRequestEntity purchaseRequest = purchaseRequestRepository.findById(prId)
                    .orElseThrow(() -> new RuntimeException("Purchase Request not found with id: " + prId));

            // Check if the purchase request is in approved status
            if (!"APPROVED".equals(purchaseRequest.getStatus())) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "Purchase Request is not in APPROVED status");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // Set the purchase request ID in the delivery terms
            deliveryTerms.setPurchaseRequestId(prId);

            // Generate the PDF
            exportService.generatePurchaseOrderPdf(purchaseRequest, deliveryTerms, response);

            return ResponseEntity.ok().build();
        } catch (RuntimeException e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Failed to generate Purchase Order PDF: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Get vendors for a specific Purchase Request with their RFQ response status
     */
    @GetMapping("/{prId}/vendors")
    @Operation(
        summary = "Get vendors for a Purchase Request with RFQ response status",
        description = "Retrieves all vendors associated with a specific Purchase Request along with their response status " +
                "(whether they have submitted quotations or not). This is used for the RFQ email resend functionality.",
        tags = {"Purchase Request Workflow"}
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Vendors retrieved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = VendorRfqStatusDTO.class))
        ),
        @ApiResponse(responseCode = "404", description = "Purchase request not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getVendorsForPR(
            @Parameter(description = "ID of the purchase request", required = true)
            @PathVariable Long prId) {
        try {
            List<VendorRfqStatusDTO> vendors = purchaseRequestService.getVendorsForPR(prId);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", vendors);
            response.put("count", vendors.size());
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to retrieve vendors: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Resend RFQ emails to selected vendors
     */
    @PostMapping("/{prId}/resend-rfq-emails")
    @Operation(
        summary = "Resend RFQ emails to selected vendors",
        description = "Resends RFQ (Request for Quotation) emails to selected vendors who haven't responded or need a reminder. " +
                "This allows users to follow up with vendors who missed the initial email or need additional time to respond.",
        tags = {"Purchase Request Workflow"}
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "RFQ emails sent successfully"
        ),
        @ApiResponse(responseCode = "400", description = "Invalid input or purchase request not in valid state"),
        @ApiResponse(responseCode = "404", description = "Purchase request not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> resendRfqEmails(
            @Parameter(description = "ID of the purchase request", required = true)
            @PathVariable Long prId,
            @Parameter(description = "List of vendor IDs to send emails to", required = true)
            @RequestBody Map<String, List<Long>> requestBody) {
        try {
            List<Long> vendorIds = requestBody.get("vendorIds");
            if (vendorIds == null || vendorIds.isEmpty()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "Vendor IDs are required");
                return ResponseEntity.badRequest().body(response);
            }

            int emailsSent = purchaseRequestService.resendRfqEmails(prId, vendorIds);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "RFQ emails sent successfully");
            response.put("emailsSent", emailsSent);
            response.put("totalVendors", vendorIds.size());
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException | IllegalStateException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to send RFQ emails: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Get approved PO details for management approval
     */
    @GetMapping("/{prId}/approved-po")
    @Operation(
        summary = "Get approved PO details for management approval",
        description = "Retrieves the approved Purchase Order details including line items with rates and grand total. " +
                "This is used in the management section to show the confirmation page before final approval.",
        tags = {"Purchase Request Workflow"}
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Approved PO details retrieved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = ApprovedPODTO.class))
        ),
        @ApiResponse(responseCode = "400", description = "Purchase request not in valid state"),
        @ApiResponse(responseCode = "404", description = "Purchase request not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getApprovedPODetails(
            @Parameter(description = "ID of the purchase request", required = true)
            @PathVariable Long prId) {
        try {
            ApprovedPODTO approvedPO = purchaseRequestService.getApprovedPODetails(prId);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", approvedPO);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } catch (IllegalStateException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to retrieve approved PO details: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Revise payment amount for a purchase request
     */
    @PostMapping("/{prId}/revise-payment")
    @Operation(
        summary = "Revise payment amount for a purchase request",
        description = "Allows management to revise the grand total amount for a purchase request before final approval. " +
                "This is used when the management wants to adjust the payment amount.",
        tags = {"Purchase Request Workflow"}
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Payment revised successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = ApprovedPODTO.class))
        ),
        @ApiResponse(responseCode = "400", description = "Invalid input or purchase request not in valid state"),
        @ApiResponse(responseCode = "404", description = "Purchase request not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> revisePayment(
            @Parameter(description = "ID of the purchase request", required = true)
            @PathVariable Long prId,
            @Parameter(description = "Payment revision details", required = true)
            @RequestBody PaymentRevisionDTO paymentRevision) {
        try {
            ApprovedPODTO updatedPO = purchaseRequestService.revisePayment(prId, paymentRevision);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", updatedPO);
            response.put("message", "Payment revised successfully");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (IllegalStateException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to revise payment: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Submit approved PO for final approval
     */
    @PostMapping("/{prId}/submit-approved-po")
    @Operation(
        summary = "Submit approved PO for final approval",
        description = "Submits the approved Purchase Order for final approval. This can be done with or without " +
                "payment revision. If no revised amount is provided, the original grand total is used.",
        tags = {"Purchase Request Workflow"}
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Purchase Order approved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = PurchaseRequestDTO.class))
        ),
        @ApiResponse(responseCode = "400", description = "Purchase request not in valid state"),
        @ApiResponse(responseCode = "404", description = "Purchase request not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> submitApprovedPO(
            @Parameter(description = "ID of the purchase request", required = true)
            @PathVariable Long prId,
            @Parameter(description = "Optional final grand total amount")
            @RequestBody(required = false) Map<String, Object> requestBody) {
        try {
            BigDecimal finalGrandTotal = null;
            if (requestBody != null && requestBody.containsKey("finalGrandTotal")) {
                Object grandTotalObj = requestBody.get("finalGrandTotal");
                if (grandTotalObj != null) {
                    if (grandTotalObj instanceof Number) {
                        finalGrandTotal = new BigDecimal(grandTotalObj.toString());
                    } else if (grandTotalObj instanceof String) {
                        finalGrandTotal = new BigDecimal((String) grandTotalObj);
                    }
                }
            }

            PurchaseRequestDTO updatedPR = purchaseRequestService.submitApprovedPO(prId, finalGrandTotal);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", updatedPR);
            response.put("message", "Purchase Order approved successfully");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } catch (IllegalStateException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to approve Purchase Order: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Get approved PO details for approved dashboard
     */
    @GetMapping("/{prId}/approved-po-dashboard")
    @Operation(
        summary = "Get approved PO details for approved dashboard",
        description = "Retrieves the approved Purchase Order details for the approved dashboard. " +
                "This endpoint is specifically for viewing already approved POs with line items, rates, and grand total. " +
                "Only works for PRs with APPROVED status.",
        tags = {"Purchase Request Workflow"}
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Approved PO details retrieved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = ApprovedPODTO.class))
        ),
        @ApiResponse(responseCode = "400", description = "Purchase request is not in APPROVED status"),
        @ApiResponse(responseCode = "404", description = "Purchase request not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getApprovedPODetailsForDashboard(
            @Parameter(description = "ID of the purchase request", required = true, example = "1")
            @PathVariable Long prId) {
        try {
            ApprovedPODTO approvedPO = purchaseRequestService.getApprovedPODetailsForDashboard(prId);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", approvedPO);
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } catch (IllegalStateException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to retrieve approved PO details: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Get vendor-split PO details for management tab
     */
    @GetMapping("/{prId}/vendor-split")
    @Operation(
        summary = "Get vendor-split PO details for management tab",
        description = "Splits a Purchase Request into vendor-specific POs for individual management approval. " +
                "Each vendor gets their own PO with their selected line items, allowing separate approval and payment revision. " +
                "Only works for PRs with IN_PROGRESS_MANAGEMENT status.",
        tags = {"Purchase Request Workflow"}
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Vendor-split PO details retrieved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = VendorSplitPODTO.class))
        ),
        @ApiResponse(responseCode = "400", description = "Purchase request is not in IN_PROGRESS_MANAGEMENT status"),
        @ApiResponse(responseCode = "404", description = "Purchase request not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getVendorSplitPODetails(
            @Parameter(description = "ID of the purchase request", required = true, example = "100")
            @PathVariable Long prId) {
        try {
            List<VendorSplitPODTO> vendorPOs = purchaseRequestService.getVendorSplitPODetails(prId);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", vendorPOs);
            response.put("count", vendorPOs.size());
            response.put("message", "Vendor-split PO details retrieved successfully");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } catch (IllegalStateException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to retrieve vendor-split PO details: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Revise payment for a specific vendor in vendor-split workflow
     */
    @PostMapping("/{prId}/vendor/{vendorId}/revise-payment")
    @Operation(
        summary = "Revise payment for a specific vendor in vendor-split workflow",
        description = "Revises the grand total for a specific vendor in the management tab. " +
                "This allows management to apply discounts or adjust payment amounts for individual vendors " +
                "without affecting other vendors in the same PR. Only works for PRs with IN_PROGRESS_MANAGEMENT status.",
        tags = {"Purchase Request Workflow"}
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Vendor payment revised successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = VendorSplitPODTO.class))
        ),
        @ApiResponse(responseCode = "400", description = "Purchase request is not in IN_PROGRESS_MANAGEMENT status or invalid vendor"),
        @ApiResponse(responseCode = "404", description = "Purchase request or vendor not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> reviseVendorPayment(
            @Parameter(description = "ID of the purchase request", required = true, example = "647")
            @PathVariable Long prId,
            @Parameter(description = "ID of the vendor to revise payment for", required = true, example = "89")
            @PathVariable Long vendorId,
            @Parameter(description = "Payment revision details", required = true)
            @RequestBody Map<String, Object> requestBody) {
        try {
            BigDecimal newVendorTotal = new BigDecimal(requestBody.get("newVendorTotal").toString());
            VendorSplitPODTO updatedVendorPO = purchaseRequestService.reviseVendorPayment(prId, vendorId, newVendorTotal);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", updatedVendorPO);
            response.put("message", "Vendor payment revised successfully for " + updatedVendorPO.getVendorPoId());
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } catch (IllegalStateException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to revise vendor payment: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Approve a specific vendor in vendor-split workflow
     */
    @PostMapping("/{prId}/vendor/{vendorId}/approve")
    @Operation(
        summary = "Approve a specific vendor in vendor-split workflow",
        description = "Approves a specific vendor's portion of the PR in the management tab. " +
                "Can optionally include a revised total amount. This allows management to approve " +
                "vendors individually rather than approving the entire PR at once. " +
                "Only works for PRs with IN_PROGRESS_MANAGEMENT status.",
        tags = {"Purchase Request Workflow"}
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Vendor approved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = VendorSplitPODTO.class))
        ),
        @ApiResponse(responseCode = "400", description = "Purchase request is not in IN_PROGRESS_MANAGEMENT status or invalid vendor"),
        @ApiResponse(responseCode = "404", description = "Purchase request or vendor not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> approveVendor(
            @Parameter(description = "ID of the purchase request", required = true, example = "647")
            @PathVariable Long prId,
            @Parameter(description = "ID of the vendor to approve", required = true, example = "89")
            @PathVariable Long vendorId,
            @Parameter(description = "Approval details with optional revised total", required = false)
            @RequestBody(required = false) Map<String, Object> requestBody) {
        try {
            BigDecimal revisedTotal = null;
            if (requestBody != null && requestBody.containsKey("revisedTotal")) {
                revisedTotal = new BigDecimal(requestBody.get("revisedTotal").toString());
            }

            VendorSplitPODTO approvedVendorPO = purchaseRequestService.approveVendor(prId, vendorId, revisedTotal);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", approvedVendorPO);
            response.put("message", "Vendor approved successfully: " + approvedVendorPO.getVendorPoId());
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } catch (IllegalStateException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to approve vendor: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Submit all vendors for approval - creates separate approved POs
     */
    @PostMapping("/{prId}/submit-all-vendors")
    @Operation(
        summary = "Submit all vendors for approval - creates separate approved POs",
        description = "Approves all vendors in the PR and creates separate approved POs for each vendor. " +
                "This is the final step in the management workflow where all vendor portions are approved at once. " +
                "Each vendor gets their own approved PO (e.g., 647-1, 647-2) with individual revised payments if applicable. " +
                "The approved vendor POs will appear in the PO submission dashboard for individual PDF generation. " +
                "Only works for PRs with IN_PROGRESS_MANAGEMENT status.",
        tags = {"Purchase Request Workflow"}
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "All vendors approved successfully, separate POs created",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = VendorSplitPODTO.class))
        ),
        @ApiResponse(responseCode = "400", description = "Purchase request is not in IN_PROGRESS_MANAGEMENT status"),
        @ApiResponse(responseCode = "404", description = "Purchase request not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> submitAllVendorsForApproval(
            @Parameter(description = "ID of the purchase request", required = true, example = "647")
            @PathVariable Long prId,
            @Parameter(description = "Optional vendor payment revisions", required = false)
            @RequestBody(required = false) Map<String, Object> requestBody) {
        try {
            Map<Long, BigDecimal> vendorRevisions = null;

            // Parse vendor revisions if provided
            if (requestBody != null && requestBody.containsKey("vendorRevisions")) {
                vendorRevisions = new HashMap<>();
                @SuppressWarnings("unchecked")
                Map<String, Object> revisions = (Map<String, Object>) requestBody.get("vendorRevisions");

                for (Map.Entry<String, Object> entry : revisions.entrySet()) {
                    Long vendorId = Long.valueOf(entry.getKey());
                    BigDecimal revisedTotal = new BigDecimal(entry.getValue().toString());
                    vendorRevisions.put(vendorId, revisedTotal);
                }
            }

            List<VendorSplitPODTO> approvedVendorPOs = purchaseRequestService.submitAllVendorsForApproval(prId, vendorRevisions);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", approvedVendorPOs);
            response.put("count", approvedVendorPOs.size());
            response.put("message", "All vendors approved successfully. Created " + approvedVendorPOs.size() + " separate vendor POs.");

            // Add summary of vendor PO IDs
            List<String> vendorPoIds = approvedVendorPOs.stream()
                    .map(VendorSplitPODTO::getVendorPoId)
                    .collect(java.util.stream.Collectors.toList());
            response.put("vendorPoIds", vendorPoIds);

            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } catch (IllegalStateException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to submit all vendors for approval: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Get all approved vendor-split POs for PO submission dashboard
     */
    @GetMapping("/approved-vendor-pos")
    @Operation(
        summary = "Get all approved vendor-split POs for PO submission dashboard",
        description = "Retrieves all approved vendor-specific POs from all approved PRs. " +
                "Each vendor gets their own PO entry (e.g., 647 for single vendor, 647-1, 647-2 for multiple vendors) with their line items and totals. " +
                "This endpoint is specifically designed for the PO submission dashboard where each vendor PO " +
                "can be processed individually for PDF generation and final submission. " +
                "Returns vendor POs with APPROVED status only.",
        tags = {"Purchase Request Workflow"}
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Approved vendor POs retrieved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = VendorSplitPODTO.class))
        ),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getAllApprovedVendorPOs() {
        try {
            List<VendorSplitPODTO> approvedVendorPOs = purchaseRequestService.getAllApprovedVendorPOs();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", approvedVendorPOs);
            response.put("count", approvedVendorPOs.size());
            response.put("message", "Approved vendor POs retrieved successfully");

            // Add summary statistics
            Map<String, Object> summary = new HashMap<>();
            summary.put("totalVendorPOs", approvedVendorPOs.size());
            summary.put("uniquePRs", approvedVendorPOs.stream()
                    .map(VendorSplitPODTO::getOriginalPrId)
                    .distinct()
                    .count());
            summary.put("uniqueVendors", approvedVendorPOs.stream()
                    .map(VendorSplitPODTO::getVendorId)
                    .distinct()
                    .count());
            response.put("summary", summary);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to retrieve approved vendor POs: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Get vendor-split PO details for APPROVED PR (same structure as management view)
     */
    @GetMapping("/{prId}/approved-vendor-split")
    @Operation(
        summary = "Get vendor-split PO details for approved PR",
        description = "Retrieves vendor-split PO details for an approved PR. Returns the same data structure " +
                "as the management vendor-split view, but for approved PRs. Shows individual vendor POs " +
                "with their line items, original totals, revised payments, and vendor information. " +
                "Only works for PRs with APPROVED status.",
        tags = {"Purchase Request Workflow"}
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Approved vendor-split PO details retrieved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = VendorSplitPODTO.class))
        ),
        @ApiResponse(responseCode = "400", description = "Purchase request is not in approved status"),
        @ApiResponse(responseCode = "404", description = "Purchase request not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> getApprovedVendorSplitPODetails(
            @Parameter(description = "ID of the approved purchase request", required = true, example = "647")
            @PathVariable Long prId) {
        try {
            List<VendorSplitPODTO> vendorPOs = purchaseRequestService.getApprovedVendorSplitPODetails(prId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", vendorPOs);
            response.put("count", vendorPOs.size());
            response.put("message", "Approved vendor-split PO details retrieved successfully");

            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } catch (IllegalStateException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to retrieve approved vendor-split PO details: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Generate Purchase Order PDF for specific vendor
     */
    @PostMapping("/vendor-po/{vendorPoId}/generate-purchase-order")
    @Operation(
        summary = "Generate Purchase Order PDF for specific vendor",
        description = "Generates a PDF purchase order for a specific vendor using your existing PDF format and delivery terms structure. " +
                "Uses the vendor PO ID (e.g., 647-1) to generate individual vendor PDFs with the same format as your existing PO PDFs.",
        tags = {"Purchase Request Workflow"}
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "PDF generated successfully"),
        @ApiResponse(responseCode = "404", description = "Vendor PO not found"),
        @ApiResponse(responseCode = "400", description = "Invalid input or vendor PO not in approved status")
    })
    public ResponseEntity<?> generateVendorPurchaseOrderPdf(
            @Parameter(description = "Vendor PO ID (e.g., 647 for single vendor, 647-1 for multiple vendors)", required = true, example = "647")
            @PathVariable String vendorPoId,
            @RequestBody PurchaseOrderDeliveryTermsDTO deliveryTerms,
            HttpServletResponse response) {
        try {
            // Find vendor PO by vendor PO ID efficiently (without loading all vendor POs)
            VendorSplitPODTO vendorPO = purchaseRequestService.getVendorPOByVendorPoId(vendorPoId);

            // Set the vendor PO ID in the delivery terms for filename
            deliveryTerms.setPurchaseRequestId(vendorPO.getOriginalPrId());

            // Save delivery terms for gate pass creation
            deliveryTermsService.saveDeliveryTermsForVendorPO(vendorPO, deliveryTerms);

            // Generate the PDF using exact copy of your existing method
            exportService.generateVendorPurchaseOrderPdf(vendorPO, deliveryTerms, response);

            return ResponseEntity.ok().build();
        } catch (IllegalArgumentException e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
        } catch (IllegalStateException e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Failed to generate Vendor Purchase Order PDF: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Generate Purchase Order PDF for vendor PO by vendor PO ID - NEW API for individual vendor PDFs
     */
    @PostMapping("/vendor-po/{vendorPoId}/pdf")
    @Operation(
        summary = "Generate Purchase Order PDF for individual vendor PO by vendor PO ID",
        description = "Generates a PDF purchase order for a specific vendor using your existing PDF format. " +
                "Uses the vendor PO ID (e.g., 647-1) to generate individual vendor PDFs with the same format, " +
                "delivery terms, and 3-page structure as your existing PO PDFs. " +
                "Requires delivery terms to be provided in the request body (same as existing API).",
        tags = {"Purchase Request Workflow"}
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "PDF generated successfully"),
        @ApiResponse(responseCode = "404", description = "Vendor PO not found"),
        @ApiResponse(responseCode = "400", description = "Invalid delivery terms"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> generateIndividualVendorPOPdf(
            @Parameter(description = "Vendor PO ID (e.g., 647 for single vendor, 647-1 for multiple vendors)", required = true, example = "647")
            @PathVariable String vendorPoId,
            @RequestBody PurchaseOrderDeliveryTermsDTO deliveryTerms,
            HttpServletResponse response) {
        try {
            // Find vendor PO by vendor PO ID efficiently (without loading all vendor POs)
            VendorSplitPODTO vendorPO = purchaseRequestService.getVendorPOByVendorPoId(vendorPoId);

            // Save delivery terms for gate pass creation
            deliveryTermsService.saveDeliveryTermsForVendorPO(vendorPO, deliveryTerms);

            // Use your existing PDF generation method with vendor-specific data and user-provided delivery terms
            exportService.generateVendorPurchaseOrderPdf(vendorPO, deliveryTerms, response);

            return ResponseEntity.ok().build();

        } catch (IllegalArgumentException e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
        } catch (IllegalStateException e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "Failed to generate PDF: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Update PR line items with rates from selected quotations
     */
    @PostMapping("/{prId}/update-rates")
    @Operation(
        summary = "Update PR line items with rates from selected quotations",
        description = "Updates the purchase request line items with rates from selected quotations. " +
                "This should be called after quotations are selected to populate the rates.",
        tags = {"Purchase Request Workflow"}
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Rates updated successfully"),
        @ApiResponse(responseCode = "404", description = "Purchase request not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    public ResponseEntity<?> updatePRRates(
            @Parameter(description = "ID of the purchase request", required = true)
            @PathVariable Long prId) {
        try {
            purchaseRequestService.updatePRItemsWithSelectedQuotationRates(prId);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "PR line items updated with selected quotation rates successfully");
            return ResponseEntity.ok(response);
        } catch (IllegalArgumentException e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Failed to update rates: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
