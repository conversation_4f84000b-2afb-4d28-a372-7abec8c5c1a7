package com.synergy.mapper;

import com.synergy.dto.PurchaseRequestLightDTO;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Mapper class to convert native query results to DTOs
 */
public class PurchaseRequestMapper {

    /**
     * Maps native query results to PurchaseRequestLightDTO objects
     *
     * @param results List of Object arrays from native query
     * @return List of PurchaseRequestLightDTO objects
     */
    public static List<PurchaseRequestLightDTO> mapToPurchaseRequestLightDTOs(List<Object[]> results) {
        List<PurchaseRequestLightDTO> dtos = new ArrayList<>();

        for (Object[] result : results) {
            PurchaseRequestLightDTO dto = new PurchaseRequestLightDTO();

            // Map each column to the corresponding DTO field
            // Column order: id, pr_id, created_date, client_name, yard_number, project_name, line_item_count, status, pr_type, rfq_status, remarks
            int i = 0;

            // ID (Long)
            if (result[i] != null) {
                if (result[i] instanceof BigInteger) {
                    dto.setId(((BigInteger) result[i]).longValue());
                } else {
                    dto.setId(((Number) result[i]).longValue());
                }
            }
            i++;

            // PR ID (String)
            if (result[i] != null) {
                dto.setPrId((String) result[i]);
            }
            i++;

            // Created Date (Date)
            if (result[i] != null) {
                if (result[i] instanceof Timestamp) {
                    dto.setCreatedDate(new Date(((Timestamp) result[i]).getTime()));
                } else {
                    dto.setCreatedDate((Date) result[i]);
                }
            }
            i++;

            // Client Name (String)
            if (result[i] != null) {
                dto.setClientName((String) result[i]);
            }
            i++;

            // Yard Number (String)
            if (result[i] != null) {
                dto.setYardNumber((String) result[i]);
            }
            i++;

            // Project Name (String)
            if (result[i] != null) {
                dto.setProjectName((String) result[i]);
            }
            i++;

            // Line Item Count (Integer)
            if (result[i] != null) {
                if (result[i] instanceof BigInteger) {
                    dto.setLineItemCount(((BigInteger) result[i]).intValue());
                } else {
                    dto.setLineItemCount(((Number) result[i]).intValue());
                }
            }
            i++;

            // Status (String)
            if (result[i] != null) {
                dto.setStatus((String) result[i]);
            }
            i++;

            // PR Type (String)
            if (i < result.length && result[i] != null) {
                dto.setPrType((String) result[i]);
            }
            i++;

            // RFQ Status (String) - only for queries that include it
            if (i < result.length && result[i] != null) {
                dto.setRfqStatus((String) result[i]);
            }
            i++;

            // Remarks (String) - only for queries that include it
            if (i < result.length && result[i] != null) {
                dto.setRemarks((String) result[i]);
            }

            dtos.add(dto);
        }

        return dtos;
    }

    /**
     * Maps native query results to PurchaseRequestLightDTO objects for creation workflow
     * Handles queries that return: id, pr_id, created_date, client_name, yard_number, project_name, line_item_count, status, pr_type, remarks, material_families
     *
     * @param results List of Object arrays from native query
     * @return List of PurchaseRequestLightDTO objects
     */
    public static List<PurchaseRequestLightDTO> mapToPurchaseRequestLightDTOsForCreation(List<Object[]> results) {
        List<PurchaseRequestLightDTO> dtos = new ArrayList<>();

        for (Object[] result : results) {
            PurchaseRequestLightDTO dto = new PurchaseRequestLightDTO();

            // Map each column to the corresponding DTO field
            // Column order: id, pr_id, created_date, client_name, yard_number, project_name, line_item_count, status, pr_type, remarks
            int i = 0;

            // ID (Long)
            if (result[i] != null) {
                if (result[i] instanceof BigInteger) {
                    dto.setId(((BigInteger) result[i]).longValue());
                } else {
                    dto.setId(((Number) result[i]).longValue());
                }
            }
            i++;

            // PR ID (String)
            if (result[i] != null) {
                dto.setPrId((String) result[i]);
            }
            i++;

            // Created Date (Date)
            if (result[i] != null) {
                if (result[i] instanceof Timestamp) {
                    dto.setCreatedDate(new Date(((Timestamp) result[i]).getTime()));
                } else {
                    dto.setCreatedDate((Date) result[i]);
                }
            }
            i++;

            // Client Name (String)
            if (result[i] != null) {
                dto.setClientName((String) result[i]);
            }
            i++;

            // Yard Number (String)
            if (result[i] != null) {
                dto.setYardNumber((String) result[i]);
            }
            i++;

            // Project Name (String)
            if (result[i] != null) {
                dto.setProjectName((String) result[i]);
            }
            i++;

            // Line Item Count (Integer)
            if (result[i] != null) {
                if (result[i] instanceof BigInteger) {
                    dto.setLineItemCount(((BigInteger) result[i]).intValue());
                } else {
                    dto.setLineItemCount(((Number) result[i]).intValue());
                }
            }
            i++;

            // Status (String)
            if (result[i] != null) {
                dto.setStatus((String) result[i]);
            }
            i++;

            // PR Type (String)
            if (result[i] != null) {
                dto.setPrType((String) result[i]);
            }
            i++;

            // Remarks (String)
            if (result[i] != null) {
                dto.setRemarks((String) result[i]);
            }
            i++;

            // Material Families (String - comma separated)
            if (i < result.length && result[i] != null) {
                String materialFamiliesStr = (String) result[i];
                if (materialFamiliesStr != null && !materialFamiliesStr.trim().isEmpty()) {
                    dto.setMaterialFamilies(materialFamiliesStr.trim());
                }
            }

            dtos.add(dto);
        }

        return dtos;
    }

    /**
     * Maps native query results to PurchaseRequestLightDTO objects for in-progress-management workflow
     * Handles queries that return: id, pr_id, created_date, client_name, yard_number, project_name, line_item_count, status, pr_type, remarks, material_families
     *
     * @param results List of Object arrays from native query
     * @return List of PurchaseRequestLightDTO objects
     */
    public static List<PurchaseRequestLightDTO> mapToPurchaseRequestLightDTOsForInProgressManagement(List<Object[]> results) {
        List<PurchaseRequestLightDTO> dtos = new ArrayList<>();

        for (Object[] result : results) {
            PurchaseRequestLightDTO dto = new PurchaseRequestLightDTO();

            // Map each column to the corresponding DTO field
            // Column order: id, pr_id, created_date, client_name, yard_number, project_name, line_item_count, status, pr_type, remarks
            int i = 0;

            // ID (Long)
            if (result[i] != null) {
                if (result[i] instanceof BigInteger) {
                    dto.setId(((BigInteger) result[i]).longValue());
                } else {
                    dto.setId(((Number) result[i]).longValue());
                }
            }
            i++;

            // PR ID (String)
            if (result[i] != null) {
                dto.setPrId((String) result[i]);
            }
            i++;

            // Created Date (Date)
            if (result[i] != null) {
                if (result[i] instanceof Timestamp) {
                    dto.setCreatedDate(new Date(((Timestamp) result[i]).getTime()));
                } else {
                    dto.setCreatedDate((Date) result[i]);
                }
            }
            i++;

            // Client Name (String)
            if (result[i] != null) {
                dto.setClientName((String) result[i]);
            }
            i++;

            // Yard Number (String)
            if (result[i] != null) {
                dto.setYardNumber((String) result[i]);
            }
            i++;

            // Project Name (String)
            if (result[i] != null) {
                dto.setProjectName((String) result[i]);
            }
            i++;

            // Line Item Count (Integer)
            if (result[i] != null) {
                if (result[i] instanceof BigInteger) {
                    dto.setLineItemCount(((BigInteger) result[i]).intValue());
                } else {
                    dto.setLineItemCount(((Number) result[i]).intValue());
                }
            }
            i++;

            // Status (String)
            if (result[i] != null) {
                dto.setStatus((String) result[i]);
            }
            i++;

            // PR Type (String)
            if (result[i] != null) {
                dto.setPrType((String) result[i]);
            }
            i++;

            // Remarks (String)
            if (result[i] != null) {
                dto.setRemarks((String) result[i]);
            }
            i++;

            // Material Families (String - comma separated)
            if (i < result.length && result[i] != null) {
                String materialFamiliesStr = (String) result[i];
                if (materialFamiliesStr != null && !materialFamiliesStr.trim().isEmpty()) {
                    dto.setMaterialFamilies(materialFamiliesStr.trim());
                }
            }

            dtos.add(dto);
        }

        return dtos;
    }
}
